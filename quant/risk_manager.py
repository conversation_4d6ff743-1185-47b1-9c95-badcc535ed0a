"""
Risk Management Module

Handles all risk management operations including position sizing, loss limits,
and trading suspension logic.
"""

import json
from dataclasses import dataclass
from datetime import date
from enum import Enum
from typing import Any

from quant.utils.logger import get_logger


class RiskStatus(Enum):
    """Risk status enumeration."""

    NORMAL = "normal"
    WARNING = "warning"
    SUSPENDED = "suspended"
    CRITICAL = "critical"


@dataclass
class RiskLimits:
    """Risk limits configuration."""

    max_daily_loss_percent: float = 0.10  # 10% daily loss limit
    max_consecutive_losses: int = 3
    max_position_size_usdt: float = 100.0
    min_position_size_usdt: float = 5.0
    base_position_size_usdt: float = 20.0
    volatility_threshold: float = 0.3  # High volatility threshold
    confidence_threshold: float = 0.6  # Minimum confidence for trading


@dataclass
class AccountState:
    """Current account state."""

    balance_usdt: float = 1000.0
    daily_pnl: float = 0.0
    consecutive_losses: int = 0
    last_trade_date: date | None = None
    is_suspended: bool = False
    suspension_reason: str | None = None


@dataclass
class PositionSizingResult:
    """Position sizing calculation result."""

    position_size_usdt: float
    confidence_score: float
    risk_factor: float
    position_count: int
    risk_level: RiskStatus
    notes: list[str]


class RiskManager:
    """Manages trading risk and position sizing."""

    def __init__(self, config_path: str = "config.json"):
        self.limits = RiskLimits()
        self.account_state = AccountState()
        self.logger = get_logger(__name__)

        # Load configuration
        self._load_config(config_path)

        # Initialize risk metrics
        self._initialize_metrics()

    def _load_config(self, config_path: str):
        """Load risk management configuration."""
        try:
            with open(config_path, encoding="utf-8") as f:
                config = json.load(f)

            risk_config = config.get("risk_management", {})

            # Update limits from config
            self.limits.max_daily_loss_percent = risk_config.get(
                "max_daily_loss_percent", 0.10
            )
            self.limits.max_consecutive_losses = risk_config.get(
                "max_consecutive_losses", 3
            )
            self.limits.max_position_size_usdt = risk_config.get(
                "max_position_size_usdt", 100.0
            )
            self.limits.min_position_size_usdt = risk_config.get(
                "min_position_size_usdt", 5.0
            )
            self.limits.base_position_size_usdt = risk_config.get(
                "base_position_size_usdt", 20.0
            )
            self.limits.volatility_threshold = risk_config.get(
                "volatility_threshold", 0.3
            )
            self.limits.confidence_threshold = risk_config.get(
                "confidence_threshold", 0.6
            )

            # Load account state
            account_config = risk_config.get("account_state", {})
            self.account_state.balance_usdt = account_config.get("balance_usdt", 1000.0)
            self.account_state.daily_pnl = account_config.get("daily_pnl", 0.0)
            self.account_state.consecutive_losses = account_config.get(
                "consecutive_losses", 0
            )

            self.logger.info("Risk management configuration loaded successfully")

        except Exception as e:
            self.logger.error(f"Error loading risk configuration: {e}")
            # Use default values

    def _initialize_metrics(self):
        """Initialize risk tracking metrics."""
        self.daily_loss_limit = (
            self.account_state.balance_usdt * self.limits.max_daily_loss_percent
        )
        self.risk_metrics = {
            "total_trades": 0,
            "winning_trades": 0,
            "losing_trades": 0,
            "max_drawdown": 0.0,
            "current_drawdown": 0.0,
            "risk_adjusted_return": 0.0,
        }

    def calculate_position_size(
        self, signal_data: dict[str, Any], market_data: dict[str, Any] | None = None
    ) -> PositionSizingResult:
        """Calculate dynamic position size based on risk factors."""

        notes = []

        # Extract signal data
        confidence_score = signal_data.get("confidence_score", 0.5)
        suggested_bet = signal_data.get(
            "suggested_bet", self.limits.base_position_size_usdt
        )

        # Calculate base risk factor
        risk_factor = 1.0
        position_count = 1

        # 1. Confidence-based adjustment
        if confidence_score < self.limits.confidence_threshold:
            risk_factor *= 0.5
            notes.append(
                f"Low confidence ({confidence_score:.2f}) - reducing position size"
            )
            position_count = 1
        elif confidence_score >= 0.8:
            risk_factor *= 1.2
            notes.append(
                f"High confidence ({confidence_score:.2f}) - increasing position size"
            )
            position_count = 2
        elif confidence_score >= 0.9:
            risk_factor *= 1.5
            notes.append(
                f"Very high confidence ({confidence_score:.2f}) - maximum position size"
            )
            position_count = 3

        # 2. Market volatility adjustment
        if market_data:
            volatility = market_data.get("volatility", 0.2)
            if volatility > self.limits.volatility_threshold:
                risk_factor *= 0.7
                notes.append(f"High volatility ({volatility:.2f}) - reducing exposure")

        # 3. Account state adjustment
        if self.account_state.consecutive_losses >= 2:
            risk_factor *= 0.6
            notes.append(
                f"Consecutive losses ({self.account_state.consecutive_losses}) - reducing risk"
            )

        if self.account_state.daily_pnl < 0:
            loss_ratio = (
                abs(self.account_state.daily_pnl) / self.account_state.balance_usdt
            )
            if loss_ratio > 0.5:  # 50% of daily loss limit
                risk_factor *= 0.5
                notes.append(
                    f"Approaching daily loss limit ({loss_ratio:.1%}) - reducing position size"
                )

        # 4. Calculate final position size
        base_size = min(suggested_bet, self.limits.base_position_size_usdt)
        final_size = base_size * risk_factor

        # Apply limits
        final_size = max(final_size, self.limits.min_position_size_usdt)
        final_size = min(final_size, self.limits.max_position_size_usdt)

        # Ensure we have enough balance
        max_affordable = (
            self.account_state.balance_usdt * 0.1
        )  # Max 10% of balance per trade
        final_size = min(final_size, max_affordable)

        # Determine risk level
        if risk_factor >= 1.2:
            risk_level = RiskStatus.NORMAL
        elif risk_factor >= 0.8:
            risk_level = RiskStatus.WARNING
        else:
            risk_level = RiskStatus.CRITICAL

        # Round to nearest USDT
        final_size = round(final_size, 2)

        return PositionSizingResult(
            position_size_usdt=final_size,
            confidence_score=confidence_score,
            risk_factor=risk_factor,
            position_count=position_count,
            risk_level=risk_level,
            notes=notes,
        )

    def check_risk_limits(self, current_date: date = None) -> RiskStatus:
        """Check if trading should be suspended due to risk limits."""

        if current_date is None:
            current_date = date.today()

        # Check for new day reset
        if self.account_state.last_trade_date != current_date:
            self._reset_daily_metrics(current_date)

        # Check daily loss limit
        daily_loss_limit = (
            self.account_state.balance_usdt * self.limits.max_daily_loss_percent
        )
        if self.account_state.daily_pnl <= -daily_loss_limit:
            self.account_state.is_suspended = True
            self.account_state.suspension_reason = (
                f"Daily loss limit exceeded (${daily_loss_limit:.2f})"
            )
            self.logger.warning(
                f"Trading suspended: {self.account_state.suspension_reason}"
            )
            return RiskStatus.SUSPENDED

        # Check consecutive losses
        if self.account_state.consecutive_losses >= self.limits.max_consecutive_losses:
            self.account_state.is_suspended = True
            self.account_state.suspension_reason = f"Maximum consecutive losses reached ({self.limits.max_consecutive_losses})"
            self.logger.warning(
                f"Trading suspended: {self.account_state.suspension_reason}"
            )
            return RiskStatus.SUSPENDED

        # Check for warning conditions
        if self.account_state.consecutive_losses >= 2:
            return RiskStatus.WARNING

        if self.account_state.daily_pnl <= -daily_loss_limit * 0.7:
            return RiskStatus.WARNING

        return RiskStatus.NORMAL

    def update_trade_result(self, pnl: float, trade_date: date = None):
        """Update account state with trade result."""

        if trade_date is None:
            trade_date = date.today()

        # Update daily P&L
        self.account_state.daily_pnl += pnl
        self.account_state.balance_usdt += pnl

        # Update consecutive losses counter
        if pnl < 0:
            self.account_state.consecutive_losses += 1
        else:
            self.account_state.consecutive_losses = 0

        # Update trade date
        self.account_state.last_trade_date = trade_date

        # Update risk metrics
        self._update_risk_metrics(pnl)

        # Check risk limits after update
        risk_status = self.check_risk_limits(trade_date)

        self.logger.info(
            f"Trade result: P&L=${pnl:.2f}, Balance=${self.account_state.balance_usdt:.2f}, "
            f"Daily P&L=${self.account_state.daily_pnl:.2f}, Risk status: {risk_status.value}"
        )

    def _reset_daily_metrics(self, current_date: date):
        """Reset daily metrics for new day."""
        self.account_state.daily_pnl = 0.0
        self.account_state.last_trade_date = current_date
        self.account_state.is_suspended = False
        self.account_state.suspension_reason = None
        
        # Reset P&L history for new day's calculations
        if hasattr(self, '_trade_pnl_history'):
            self._trade_pnl_history.clear()

    def reset_suspension(self, reason: str = "Manual reset"):
        """Manually reset trading suspension state."""
        self.account_state.is_suspended = False
        self.account_state.suspension_reason = None
        self.account_state.consecutive_losses = 0  # Reset consecutive losses as well
        
        self.logger.info(f"Trading suspension manually reset: {reason}")
        
        # Reset P&L history to avoid immediate re-suspension
        if hasattr(self, '_trade_pnl_history'):
            self._trade_pnl_history.clear()
            
        self.logger.info("Daily risk metrics reset")

    def _update_risk_metrics(self, pnl: float):
        """Update risk tracking metrics."""
        self.risk_metrics["total_trades"] += 1

        if pnl > 0:
            self.risk_metrics["winning_trades"] += 1
        else:
            self.risk_metrics["losing_trades"] += 1

        # Calculate current drawdown
        if self.account_state.daily_pnl < 0:
            self.risk_metrics["current_drawdown"] = abs(self.account_state.daily_pnl)
            self.risk_metrics["max_drawdown"] = max(
                self.risk_metrics["max_drawdown"], self.risk_metrics["current_drawdown"]
            )

        # Calculate risk-adjusted return with proper averaging
        if self.risk_metrics["total_trades"] > 0:
            win_rate = (
                self.risk_metrics["winning_trades"] / self.risk_metrics["total_trades"]
            )
            # Use actual trade data for averages instead of hardcoded values
            if hasattr(self, '_trade_pnl_history'):
                winning_trades = [p for p in self._trade_pnl_history if p > 0]
                losing_trades = [p for p in self._trade_pnl_history if p < 0]
                avg_win = sum(winning_trades) / len(winning_trades) if winning_trades else 50.0
                avg_loss = abs(sum(losing_trades) / len(losing_trades)) if losing_trades else 30.0
            else:
                avg_win, avg_loss = 50.0, 30.0  # Fallback defaults
            
            self.risk_metrics["risk_adjusted_return"] = (win_rate * avg_win) - (
                (1 - win_rate) * avg_loss
            )
            
            # Track P&L history for better calculations
            if not hasattr(self, '_trade_pnl_history'):
                self._trade_pnl_history = []
            self._trade_pnl_history.append(pnl)
            
            # Keep only last 100 trades for memory efficiency
            if len(self._trade_pnl_history) > 100:
                self._trade_pnl_history = self._trade_pnl_history[-100:]

    def get_risk_report(self) -> dict[str, Any]:
        """Get comprehensive risk management report."""

        risk_status = self.check_risk_limits()

        return {
            "risk_status": risk_status.value,
            "account_state": {
                "balance_usdt": self.account_state.balance_usdt,
                "daily_pnl": self.account_state.daily_pnl,
                "consecutive_losses": self.account_state.consecutive_losses,
                "is_suspended": self.account_state.is_suspended,
                "suspension_reason": self.account_state.suspension_reason,
            },
            "risk_limits": {
                "max_daily_loss_percent": self.limits.max_daily_loss_percent,
                "max_consecutive_losses": self.limits.max_consecutive_losses,
                "max_position_size_usdt": self.limits.max_position_size_usdt,
                "min_position_size_usdt": self.limits.min_position_size_usdt,
                "daily_loss_limit": self.account_state.balance_usdt
                * self.limits.max_daily_loss_percent,
            },
            "risk_metrics": self.risk_metrics,
        }

    def can_trade(self) -> tuple[bool, str | None]:
        """Check if trading is allowed."""
        if self.account_state.is_suspended:
            return False, self.account_state.suspension_reason

        risk_status = self.check_risk_limits()
        if risk_status == RiskStatus.SUSPENDED:
            return False, "Trading suspended due to risk limits"

        return True, None

    def save_config(self, config_path: str = "config.json"):
        """Save current risk management configuration."""
        try:
            # Load existing config
            with open(config_path, encoding="utf-8") as f:
                config = json.load(f)

            # Update risk management section
            config["risk_management"] = {
                "max_daily_loss_percent": self.limits.max_daily_loss_percent,
                "max_consecutive_losses": self.limits.max_consecutive_losses,
                "max_position_size_usdt": self.limits.max_position_size_usdt,
                "min_position_size_usdt": self.limits.min_position_size_usdt,
                "base_position_size_usdt": self.limits.base_position_size_usdt,
                "volatility_threshold": self.limits.volatility_threshold,
                "confidence_threshold": self.limits.confidence_threshold,
                "account_state": {
                    "balance_usdt": self.account_state.balance_usdt,
                    "daily_pnl": self.account_state.daily_pnl,
                    "consecutive_losses": self.account_state.consecutive_losses,
                },
            }

            # Save updated config
            with open(config_path, "w", encoding="utf-8") as f:
                json.dump(config, f, indent=2, ensure_ascii=False)

            self.logger.info("Risk management configuration saved successfully")

        except Exception as e:
            self.logger.error(f"Error saving risk configuration: {e}")


# Global risk manager instance
risk_manager = RiskManager()
