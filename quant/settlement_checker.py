"""
Settlement Checker Module

Handles trade settlement and reconciliation tasks.
"""

import asyncio
from datetime import datetime, timedelta
from typing import Any

from quant.binance_client import binance_client
from quant.database_manager import db
from quant.notification_manager import notification_manager
from quant.utils.logger import get_logger, trade_logger

logger = get_logger(__name__)


class SettlementChecker:
    """Handles trade settlement and reconciliation."""

    def __init__(self):
        self.symbol = "BTCUSDT"
        self.contract_duration = timedelta(minutes=10)

    async def check_pending_settlements(self) -> list[dict[str, Any]]:
        """Check and settle all pending trades."""
        try:
            pending_trades = db.get_pending_trades()
            settled_trades = []

            for trade in pending_trades:
                # Check if trade should be settled (10 minutes after signal)
                signal_time = trade["signal_timestamp"]
                if isinstance(signal_time, str):
                    signal_time = datetime.fromisoformat(signal_time.replace('Z', '+00:00'))
                
                current_time = datetime.utcnow()
                
                # Validate settlement timing
                timing_info = self._validate_settlement_timing(signal_time, current_time)
                time_since_signal = timing_info.get("time_since_signal_minutes", 0)
                
                if time_since_signal >= 10 and time_since_signal <= 11:
                    logger.info(f"Settling trade {trade['id']} - Time since signal: {time_since_signal:.1f} minutes")
                    settlement_result = await self._settle_trade(trade)
                    if settlement_result:
                        # Add timing validation info to settlement result
                        settlement_result.update(timing_info)
                        settled_trades.append(settlement_result)

                        # Update database
                        db.update_trade_result(trade["id"], settlement_result)

                        # Send enhanced notification
                        notification_manager.send_enhanced_settlement_notification(
                            settlement_result
                        )

                        trade_logger.log_settlement(settlement_result)
                elif time_since_signal > 11:
                    logger.warning(f"Trade {trade['id']} missed settlement window - Time since signal: {time_since_signal:.1f} minutes")
                    # Force settle overdue trades but mark as delayed
                    settlement_result = await self._settle_trade(trade)
                    if settlement_result:
                        settlement_result["settlement_delayed"] = True
                        settlement_result["delay_minutes"] = time_since_signal - 10
                        settlement_result.update(timing_info)
                        settled_trades.append(settlement_result)
                        
                        # Update database
                        db.update_trade_result(trade["id"], settlement_result)
                        
                        # Send enhanced notification
                        notification_manager.send_enhanced_settlement_notification(
                            settlement_result
                        )
                        
                        trade_logger.log_settlement(settlement_result)

            return settled_trades

        except Exception as e:
            logger.error(f"Error in settlement check: {e}")
            return []

    async def check_pending_settlements_concurrent(
        self, max_concurrent: int = 5
    ) -> list[dict[str, Any]]:
        """Check and settle all pending trades with concurrent processing."""
        try:
            pending_trades = db.get_pending_trades()
            settled_trades = []

            # Create semaphore to limit concurrent settlements
            semaphore = asyncio.Semaphore(max_concurrent)

            async def settle_trade_with_semaphore(trade):
                async with semaphore:
                    # Check if trade should be settled (10 minutes after signal)
                    signal_time = trade["signal_timestamp"]
                    if isinstance(signal_time, str):
                        signal_time = datetime.fromisoformat(signal_time.replace('Z', '+00:00'))
                    settlement_time = signal_time + self.contract_duration
                    current_time = datetime.utcnow()
                    
                    # Check if we're within the settlement window (10-11 minutes after signal)
                    time_since_signal = (current_time - signal_time).total_seconds() / 60
                    
                    if time_since_signal >= 10 and time_since_signal <= 11:
                        logger.info(f"Settling trade {trade['id']} - Time since signal: {time_since_signal:.1f} minutes")
                        settlement_result = await self._settle_trade(trade)
                        if settlement_result:
                            # Update database
                            db.update_trade_result(trade["id"], settlement_result)

                            # Send enhanced notification
                            notification_manager.send_enhanced_settlement_notification(
                                settlement_result
                            )

                            trade_logger.log_settlement(settlement_result)
                            return settlement_result
                    elif time_since_signal > 11:
                        logger.warning(f"Trade {trade['id']} missed settlement window - Time since signal: {time_since_signal:.1f} minutes")
                        # Force settle overdue trades but mark as delayed
                        settlement_result = await self._settle_trade(trade)
                        if settlement_result:
                            settlement_result["settlement_delayed"] = True
                            settlement_result["delay_minutes"] = time_since_signal - 10
                            
                            # Update database
                            db.update_trade_result(trade["id"], settlement_result)
                            
                            # Send enhanced notification
                            notification_manager.send_enhanced_settlement_notification(
                                settlement_result
                            )
                            
                            trade_logger.log_settlement(settlement_result)
                            return settlement_result
                    return None

            # Process trades concurrently
            tasks = [settle_trade_with_semaphore(trade) for trade in pending_trades]
            results = await asyncio.gather(*tasks, return_exceptions=True)

            # Collect successful settlements
            for result in results:
                if result and not isinstance(result, Exception):
                    settled_trades.append(result)
                elif isinstance(result, Exception):
                    logger.error(f"Error in concurrent settlement: {result}")

            return settled_trades

        except Exception as e:
            logger.error(f"Error in concurrent settlement check: {e}")
            return []

    async def _settle_trade(self, trade: dict[str, Any]) -> dict[str, Any]:
        """Settle a single trade with data consistency validation."""
        try:
            # Data consistency validation - ensure all required fields are present
            if not self._validate_trade_data(trade):
                logger.error(f"Trade {trade['id']} has invalid data format")
                return {}

            # Get original trade data from database for consistency
            original_trade = self._get_original_trade_data(trade["id"])
            if not original_trade:
                logger.error(f"Could not retrieve original trade data for trade {trade['id']}")
                return {}

            # Calculate exact settlement time (signal_time + 10 minutes)
            signal_time = trade["signal_timestamp"]
            if isinstance(signal_time, str):
                signal_time = datetime.fromisoformat(signal_time.replace('Z', '+00:00'))
            settlement_time = signal_time + self.contract_duration
            
            # Get real-time market price at settlement time
            # This is the correct logic: get current market price when settling
            settlement_price = await binance_client.get_current_price()
            logger.info(f"Using real-time market price for settlement: ${settlement_price:,.2f}")

            # Use original trade data for consistency
            entry_price = original_trade["entry_price"]
            direction = original_trade["direction"]
            suggested_bet = original_trade["suggested_bet"]

            # Calculate P&L based on direction using settlement price
            if direction == "LONG":
                pnl = (
                    (settlement_price - entry_price)
                    * suggested_bet
                    / entry_price
                )
            else:  # SHORT
                pnl = (
                    (entry_price - settlement_price)
                    * suggested_bet
                    / entry_price
                )

            # Calculate P&L percentage
            pnl_percentage = (
                (pnl / suggested_bet) * 100
                if suggested_bet > 0
                else 0
            )

            # Determine result
            status = "WIN" if pnl > 0 else "LOSS"

            # Create settlement data with traceability information
            settlement_data = {
                "exit_price": settlement_price,
                "exit_timestamp": settlement_time.isoformat(),
                "pnl": pnl,
                "pnl_percentage": pnl_percentage,
                "status": status,
                "trade_id": trade["id"],
                "entry_price": entry_price,
                "direction": direction,
                "entry_timestamp": original_trade["signal_timestamp"],
                # Original signal traceability data
                "original_signal_id": self._calculate_signal_kline_number(original_trade["signal_timestamp"]),
                "signal_confidence": original_trade.get("confidence_score", 0.0),
                "signal_market_state": original_trade.get("market_state", "Unknown"),
                "signal_trigger_pattern": original_trade.get("trigger_pattern", "Unknown"),
                "signal_confirmed_indicators": original_trade.get("confirmed_indicators", []),
                "calculated_settlement_time": settlement_time.isoformat(),
                "actual_settlement_time": datetime.utcnow().isoformat(),
                "time_difference_minutes": (datetime.utcnow() - settlement_time).total_seconds() / 60,
                "data_consistency_verified": True,
                "symbol": original_trade.get("symbol", "BTCUSDT"),
                "suggested_bet": suggested_bet,
                "price_source": "real_time_market_price",
                "settlement_method": "current_market_price",
            }

            # Add decision details if available
            if "decision_details" in original_trade:
                settlement_data["original_decision_details"] = original_trade["decision_details"]

            # Perform consistency validation
            signal_data_for_validation = {
                "signal_timestamp": original_trade["signal_timestamp"],
                "entry_price": original_trade["entry_price"],
                "direction": original_trade["direction"]
            }
            
            consistency_check = self.validate_signal_settlement_consistency(signal_data_for_validation, settlement_data)
            settlement_data["consistency_check_passed"] = consistency_check

            logger.info(f"Trade {trade['id']} settled: {status} with P&L ${pnl:.2f}")
            logger.info(f"Settlement details: Entry ${entry_price:,.2f}, Exit ${settlement_price:,.2f}, Direction {direction}")
            logger.info(f"Settlement time calculation: Signal {signal_time}, Settlement {settlement_time}, Current {datetime.utcnow()}")
            logger.info(f"Consistency check: {'PASSED' if consistency_check else 'FAILED'}")
            return settlement_data

        except Exception as e:
            logger.error(f"Error settling trade {trade['id']}: {e}")
            return {}
    
    def _validate_trade_data(self, trade: dict[str, Any]) -> bool:
        """Validate trade data structure and required fields."""
        required_fields = ["id", "signal_timestamp", "direction", "entry_price", "suggested_bet"]
        
        for field in required_fields:
            if field not in trade:
                logger.error(f"Missing required field '{field}' in trade data")
                return False
        
        # Validate data types
        try:
            if not isinstance(trade["id"], int):
                return False
            
            if trade["direction"] not in ["LONG", "SHORT"]:
                return False
            
            if not isinstance(trade["entry_price"], (int, float)) or trade["entry_price"] <= 0:
                return False
            
            if not isinstance(trade["suggested_bet"], (int, float)) or trade["suggested_bet"] <= 0:
                return False
            
            return True
        except Exception as e:
            logger.error(f"Error validating trade data: {e}")
            return False
    
    def _get_original_trade_data(self, trade_id: int) -> dict[str, Any] | None:
        """Retrieve original trade data from database for consistency."""
        try:
            with db.get_session() as session:
                from quant.database_manager import TradeHistory
                original_trade = session.query(TradeHistory).filter(TradeHistory.id == trade_id).first()
                
                if original_trade:
                    # Generate signal identifier in X/Y format
                    signal_identifier = self._generate_signal_identifier(original_trade)
                    
                    return {
                        "id": original_trade.id,
                        "signal_timestamp": original_trade.signal_timestamp.isoformat(),
                        "symbol": original_trade.symbol,
                        "direction": original_trade.direction,
                        "entry_price": original_trade.entry_price,
                        "confidence_score": original_trade.confidence_score,
                        "market_state": original_trade.market_state,
                        "trigger_pattern": original_trade.trigger_pattern,
                        "confirmed_indicators": original_trade.confirmed_indicators,
                        "suggested_bet": original_trade.suggested_bet,
                        "decision_details": original_trade.decision_details,
                        "status": original_trade.status,
                        "signal_identifier": signal_identifier,
                    }
                else:
                    logger.error(f"Original trade with ID {trade_id} not found in database")
                    return None
                    
        except Exception as e:
            logger.error(f"Error retrieving original trade data for ID {trade_id}: {e}")
            return None
    
    def _generate_signal_identifier(self, trade) -> str:
        """Generate signal identifier in X/Y format based on daily signal count."""
        try:
            # Get the date of the signal
            signal_date = trade.signal_timestamp.date()
            
            # Count how many signals were generated on this date before this one
            with db.get_session() as session:
                from quant.database_manager import TradeHistory
                daily_signal_count = session.query(TradeHistory).filter(
                    TradeHistory.signal_timestamp >= signal_date,
                    TradeHistory.signal_timestamp < signal_date + timedelta(days=1),
                    TradeHistory.id <= trade.id
                ).count()
                
                # Format as X/Y where X is the daily sequence and Y is the total daily signals
                # For now, use a simple format with the trade ID
                return f"{daily_signal_count}/{48}"  # Assuming 48 signals per day (30min intervals)
                
        except Exception as e:
            logger.error(f"Error generating signal identifier for trade {trade.id}: {e}")
            return f"{trade.id}/48"  # Fallback format

    async def reconciliation_task(self):
        """Periodic reconciliation task to ensure data consistency."""
        try:
            # Check for orphaned trades
            orphaned_trades = self._find_orphaned_trades()

            for trade in orphaned_trades:
                logger.warning(f"Found orphaned trade: {trade.id}")
                # Force settle orphaned trades
                settlement_result = await self._settle_trade(trade)
                if settlement_result:
                    db.update_trade_result(trade.id, settlement_result)

            # Check database consistency
            self._check_database_consistency()

        except Exception as e:
            logger.error(f"Error in reconciliation task: {e}")

    def _find_orphaned_trades(self) -> list[dict[str, Any]]:
        """Find trades that are pending but should have been settled."""
        try:
            cutoff_time = datetime.utcnow() - timedelta(minutes=15)  # 15 minutes ago
            pending_trades = db.get_pending_trades()

            orphaned = [
                trade
                for trade in pending_trades
                if trade["signal_timestamp"] < cutoff_time
            ]

            return orphaned

        except Exception as e:
            logger.error(f"Error finding orphaned trades: {e}")
            return []

    def _check_database_consistency(self):
        """Check database consistency and integrity."""
        try:
            # Comprehensive consistency check for settlement data
            consistency_issues = []
            
            with db.get_session() as session:
                from quant.database_manager import TradeHistory
                
                # Check for pending trades that should have been settled
                cutoff_time = datetime.utcnow() - timedelta(minutes=15)
                overdue_trades = session.query(TradeHistory).filter(
                    TradeHistory.status == "PENDING",
                    TradeHistory.signal_timestamp < cutoff_time
                ).all()
                
                if overdue_trades:
                    consistency_issues.append(f"Found {len(overdue_trades)} overdue pending trades")
                    for trade in overdue_trades:
                        logger.warning(f"Overdue trade: ID {trade.id}, Signal time {trade.signal_timestamp}")
                
                # Check for settled trades with inconsistent data
                settled_trades = session.query(TradeHistory).filter(
                    TradeHistory.status.in_(["WIN", "LOSS"]),
                    TradeHistory.exit_timestamp.isnot(None)
                ).all()
                
                for trade in settled_trades:
                    # Validate settlement time calculation
                    expected_settlement_time = trade.signal_timestamp + timedelta(minutes=10)
                    if abs((trade.exit_timestamp - expected_settlement_time).total_seconds()) > 300:  # 5 minutes tolerance
                        consistency_issues.append(f"Trade {trade.id}: Settlement time deviation")
                        logger.warning(f"Settlement time deviation for trade {trade.id}: Expected {expected_settlement_time}, Actual {trade.exit_timestamp}")
                    
                    # Validate P&L calculation
                    if trade.exit_price and trade.entry_price and trade.pnl:
                        if trade.direction == "LONG":
                            expected_pnl = (trade.exit_price - trade.entry_price) * trade.suggested_bet / trade.entry_price
                        else:  # SHORT
                            expected_pnl = (trade.entry_price - trade.exit_price) * trade.suggested_bet / trade.entry_price
                        
                        if abs(trade.pnl - expected_pnl) > 0.01:  # 1 cent tolerance
                            consistency_issues.append(f"Trade {trade.id}: P&L calculation mismatch")
                            logger.warning(f"P&L calculation mismatch for trade {trade.id}: Expected {expected_pnl:.2f}, Actual {trade.pnl:.2f}")
                
                # Check for data integrity issues
                trades_with_missing_data = session.query(TradeHistory).filter(
                    TradeHistory.entry_price.is_(None) |
                    TradeHistory.direction.is_(None) |
                    TradeHistory.confidence_score.is_(None)
                ).all()
                
                if trades_with_missing_data:
                    consistency_issues.append(f"Found {len(trades_with_missing_data)} trades with missing required data")
                    for trade in trades_with_missing_data:
                        logger.error(f"Trade with missing data: ID {trade.id}")
            
            # Log consistency check results
            if consistency_issues:
                logger.warning(f"Database consistency check found {len(consistency_issues)} issues:")
                for issue in consistency_issues:
                    logger.warning(f"  - {issue}")
            else:
                logger.info("Database consistency check completed - no issues found")
            
            return len(consistency_issues) == 0

        except Exception as e:
            logger.error(f"Error in database consistency check: {e}")
            return False

    async def run_daily_settlement_report(self) -> dict[str, Any]:
        """Generate daily settlement report."""
        try:
            today = datetime.utcnow().date()

            # Calculate and save daily performance
            success = db.calculate_and_save_daily_performance(today)
            if not success:
                logger.warning(f"Failed to calculate daily performance for {today}")

            # Get the daily performance data
            daily_perf = db.get_daily_performance(today)

            if daily_perf:
                report = {
                    "date": daily_perf["date"],
                    "total_trades": daily_perf["total_trades"],
                    "winning_trades": daily_perf["winning_trades"],
                    "losing_trades": daily_perf["losing_trades"],
                    "win_rate": daily_perf["win_rate"],
                    "total_pnl": daily_perf["total_pnl"],
                    "generated_at": daily_perf["generated_at"],
                }
            else:
                # Fallback to old method
                start_date = datetime.combine(today, datetime.min.time())
                daily_stats = db.get_daily_stats(start_date)
                report = {
                    "date": today.isoformat(),
                    "total_trades": daily_stats["total_trades"],
                    "winning_trades": daily_stats["winning_trades"],
                    "losing_trades": daily_stats["losing_trades"],
                    "win_rate": daily_stats["win_rate"],
                    "total_pnl": 0.0,
                    "generated_at": datetime.utcnow().isoformat(),
                }

            logger.info(f"Daily settlement report: {report}")
            return report

        except Exception as e:
            logger.error(f"Error generating daily settlement report: {e}")
            return {}

    # 删除错误的 _get_settlement_kline_price 方法
    # 该方法的逻辑是错误的，应该使用实时市场价格而不是K线价格
    
    # 删除错误的 _get_kline_start_time 方法
    # 该方法与错误的K线价格获取逻辑相关，不再需要

    def _calculate_signal_kline_number(self, signal_timestamp: datetime) -> str:
        """Calculate the K-line number for signal identification in X/Y format."""
        try:
            if isinstance(signal_timestamp, str):
                signal_timestamp = datetime.fromisoformat(signal_timestamp.replace('Z', '+00:00'))
            
            # Calculate K-line number based on signal timestamp
            start_of_day = signal_timestamp.replace(hour=0, minute=0, second=0, microsecond=0)
            minutes_since_start = (signal_timestamp - start_of_day).total_seconds() / 60
            kline_number = int(minutes_since_start / 30) + 1  # 30-minute intervals, 1-indexed
            
            # Ensure valid range
            kline_number = max(1, min(kline_number, 48))
            
            return f"{kline_number}/48"
            
        except Exception as e:
            logger.error(f"Error calculating signal K-line number: {e}")
            return "Unknown/48"
    
    def validate_signal_settlement_consistency(self, signal_data: dict, settlement_data: dict) -> bool:
        """Validate that signal generation and settlement data are consistent."""
        try:
            inconsistencies = []
            
            # Check 1: Signal identifier consistency
            signal_kline = self._calculate_signal_kline_number(signal_data["signal_timestamp"])
            settlement_kline = settlement_data.get("original_signal_id", "")
            
            if signal_kline != settlement_kline:
                inconsistencies.append(f"Signal ID mismatch: Signal={signal_kline}, Settlement={settlement_kline}")
            
            # Check 2: Entry price consistency
            signal_entry_price = signal_data.get("entry_price", 0)
            settlement_entry_price = settlement_data.get("entry_price", 0)
            
            if abs(signal_entry_price - settlement_entry_price) > 0.01:
                inconsistencies.append(f"Entry price mismatch: Signal={signal_entry_price}, Settlement={settlement_entry_price}")
            
            # Check 3: Direction consistency
            signal_direction = signal_data.get("direction", "")
            settlement_direction = settlement_data.get("direction", "")
            
            if signal_direction != settlement_direction:
                inconsistencies.append(f"Direction mismatch: Signal={signal_direction}, Settlement={settlement_direction}")
            
            # Check 4: Timestamp consistency
            signal_timestamp = signal_data.get("signal_timestamp", "")
            settlement_entry_timestamp = settlement_data.get("entry_timestamp", "")
            
            if signal_timestamp != settlement_entry_timestamp:
                inconsistencies.append(f"Timestamp mismatch: Signal={signal_timestamp}, Settlement={settlement_entry_timestamp}")
            
            # Check 5: Time window compliance
            if "calculated_settlement_time" in settlement_data and "actual_settlement_time" in settlement_data:
                calculated_time = datetime.fromisoformat(settlement_data["calculated_settlement_time"].replace('Z', '+00:00'))
                actual_time = datetime.fromisoformat(settlement_data["actual_settlement_time"].replace('Z', '+00:00'))
                time_diff = abs((actual_time - calculated_time).total_seconds() / 60)
                
                if time_diff > 2:  # Allow 2 minutes tolerance
                    inconsistencies.append(f"Time window violation: {time_diff:.1f} minutes deviation")
            
            # Log validation results
            if inconsistencies:
                logger.error(f"Signal-settlement consistency check failed for {signal_kline}:")
                for issue in inconsistencies:
                    logger.error(f"  - {issue}")
                return False
            else:
                logger.info(f"Signal-settlement consistency check passed for {signal_kline}")
                return True
                
        except Exception as e:
            logger.error(f"Error in signal-settlement consistency validation: {e}")
            return False


# Global settlement checker instance
settlement_checker = SettlementChecker()
