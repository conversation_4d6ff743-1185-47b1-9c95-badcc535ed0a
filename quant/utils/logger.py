"""
Structured JSON Logger Module

Provides structured JSON logging configuration for the application.
"""

import json
import logging
import sys
from datetime import datetime
from logging.handlers import RotatingFileHandler
from pathlib import Path
from typing import Any

from quant.config_manager import config


class JSONFormatter(logging.Formatter):
    """Custom JSON formatter for structured logging."""

    def format(self, record):
        log_entry = {
            "timestamp": datetime.utcnow().isoformat(),
            "level": record.levelname,
            "logger": record.name,
            "message": record.getMessage(),
            "module": record.module,
            "function": record.funcName,
            "line": record.lineno,
        }

        # Add extra fields if available
        if hasattr(record, "extra") and record.extra:
            log_entry.update(record.extra)

        # Add exception info if available
        if record.exc_info:
            log_entry["exception"] = self.formatException(record.exc_info)

        return json.dumps(log_entry, ensure_ascii=False)


def setup_logging():
    """Set up logging configuration."""
    log_config = config.get_log_config()

    # Create logs directory if it doesn't exist
    log_path = Path(log_config.get("path", "./logs"))
    log_path.mkdir(parents=True, exist_ok=True)

    # Configure root logger
    root_logger = logging.getLogger()
    root_logger.setLevel(getattr(logging, log_config.get("level", "INFO").upper()))

    # Clear existing handlers
    for handler in root_logger.handlers[:]:
        root_logger.removeHandler(handler)

    # File handler with rotation
    if log_config.get("path"):
        file_handler = RotatingFileHandler(
            filename=log_path / log_config.get("name", "app.log"),
            maxBytes=10 * 1024 * 1024,  # 10MB
            backupCount=log_config.get("backup_count", 5),
            encoding="utf-8",
        )
        file_handler.setFormatter(JSONFormatter())
        root_logger.addHandler(file_handler)

    # Console handler
    if log_config.get("console", True):
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setFormatter(JSONFormatter())
        root_logger.addHandler(console_handler)

    # Clear log files if requested
    if log_config.get("clear", False):
        for log_file in log_path.glob("*.log"):
            log_file.unlink()


def get_logger(name: str) -> logging.Logger:
    """Get a logger instance with the specified name."""
    return logging.getLogger(name)


class TradeLogger:
    """Specialized logger for trade events."""

    def __init__(self):
        self.logger = get_logger("trading")

    def log_signal(self, signal_data: dict[str, Any]):
        """Log trading signal generation."""
        self.logger.info("Signal generated", extra={"signal_data": signal_data})

    def log_execution(self, execution_data: dict[str, Any]):
        """Log trade execution."""
        self.logger.info("Trade executed", extra={"execution_data": execution_data})

    def log_settlement(self, settlement_data: dict[str, Any]):
        """Log trade settlement."""
        self.logger.info("Trade settled", extra={"settlement_data": settlement_data})

    def log_error(self, error_data: dict[str, Any]):
        """Log trading errors."""
        self.logger.error("Trading error", extra={"error_data": error_data})


# Legacy functions for backward compatibility
def info(*args, **kwargs):
    """Legacy info function."""
    logger = get_logger("legacy")
    caller = kwargs.get("caller")
    if caller:
        msg = f"[{caller.__class__.__name__}] {' '.join(str(arg) for arg in args)}"
    else:
        msg = " ".join(str(arg) for arg in args)
    logger.info(msg)


def warning(*args, **kwargs):
    """Legacy warning function."""
    logger = get_logger("legacy")
    caller = kwargs.get("caller")
    if caller:
        msg = f"[{caller.__class__.__name__}] {' '.join(str(arg) for arg in args)}"
    else:
        msg = " ".join(str(arg) for arg in args)
    logger.warning(msg)


def debug(*args, **kwargs):
    """Legacy debug function."""
    logger = get_logger("legacy")
    caller = kwargs.get("caller")
    if caller:
        msg = f"[{caller.__class__.__name__}] {' '.join(str(arg) for arg in args)}"
    else:
        msg = " ".join(str(arg) for arg in args)
    logger.debug(msg)


def error(*args, **kwargs):
    """Legacy error function."""
    logger = get_logger("legacy")
    caller = kwargs.get("caller")
    if caller:
        msg = f"[{caller.__class__.__name__}] {' '.join(str(arg) for arg in args)}"
    else:
        msg = " ".join(str(arg) for arg in args)
    logger.error(msg)


def exception(*args, **kwargs):
    """Legacy exception function."""
    logger = get_logger("legacy")
    caller = kwargs.get("caller")
    if caller:
        msg = f"[{caller.__class__.__name__}] {' '.join(str(arg) for arg in args)}"
    else:
        msg = " ".join(str(arg) for arg in args)
    logger.exception(msg)


# Initialize logging on module import
setup_logging()

# Global logger instances
logger = get_logger(__name__)
trade_logger = TradeLogger()
