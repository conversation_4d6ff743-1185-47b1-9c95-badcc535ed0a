import ssl
import urllib3

from quant.utils.ssl_config import ssl_config

# 配置SSL警告 - 只在禁用SSL验证时禁用警告
if ssl_config.should_disable_ssl_verification():
    urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

"""
Binance Client Module

Handles all communication with Binance API and WebSocket.
"""

from collections.abc import Callable
from datetime import datetime
from typing import Any, Optional

from binance import AsyncClient, BinanceSocketManager
from binance.enums import *

from quant.config_manager import config
from quant.utils.logger import get_logger

logger = get_logger(__name__)


class BinanceClient:
    """Binance API and WebSocket client."""

    def __init__(self):
        self.client: AsyncClient | None = None
        self.bsm: BinanceSocketManager | None = None
        self.symbol = "BTCUSDT"
        self.callbacks = {}

    async def initialize(self):
        """Initialize Binance client."""
        try:
            binance_config = config.get_platform_config("binance")
            
            # Check if we're in development mode and should use mock client
            import os
            env_debug = os.getenv("ENVIRONMENT") == "development"
            config_debug = config.get("DEBUG", False)
            
            # Only use mock client if explicitly in development mode AND debug mode
            if env_debug and config_debug:
                logger.info("Using mock Binance client for development")
                self.client = None  # Will be handled by mock methods
                return
              
            # Configure SSL context for Binance client
            ssl_context = ssl_config.get_ssl_context()
            
            # Create client with SSL configuration
            self.client = await AsyncClient.create(
                api_key=binance_config.get("access_key"),
                api_secret=binance_config.get("secret_key"),
                testnet=False
            )
            logger.info("Binance client initialized successfully")
        except Exception as e:
            logger.error(f"Failed to initialize Binance client: {e}")
            # In development mode, continue with mock client
            import os
            env_debug = os.getenv("ENVIRONMENT") == "development" or os.getenv("DEBUG") == "true"
            config_debug = config.get("DEBUG", False)
            
            if env_debug or config_debug:
                logger.info("Falling back to mock Binance client for development")
                self.client = None
                return
            raise

    async def close(self):
        """Close Binance client connections."""
        if self.bsm:
            await self.bsm.close()
        if self.client:
            await self.client.close_connection()

    def register_callback(self, event_type: str, callback: Callable):
        """Register callback for specific event type."""
        self.callbacks[event_type] = callback

    async def get_klines(
        self, interval: str = KLINE_INTERVAL_30MINUTE, limit: int = 100
    ) -> list:
        """Get historical kline data."""
        if not self.client:
            # Mock data for development
            logger.info("Using mock kline data for development")
            import random
            from datetime import datetime, timedelta
            
            mock_klines = []
            base_time = datetime.now() - timedelta(hours=limit)
            base_price = 55000  # Realistic BTC price
            
            for i in range(limit):
                timestamp = base_time + timedelta(hours=i)
                price = base_price + random.uniform(-2000, 2000)
                
                mock_klines.append([
                    int(timestamp.timestamp() * 1000),  # Open time
                    price,  # Open
                    price + random.uniform(-50, 50),  # High
                    price - random.uniform(-50, 50),  # Low
                    price + random.uniform(-25, 25),  # Close
                    str(random.randint(100, 1000)),  # Volume
                    int(timestamp.timestamp() * 1000),  # Close time
                    str(random.randint(100000, 1000000)),  # Quote asset volume
                    limit,  # Number of trades
                    str(random.randint(100, 1000)),  # Taker buy base asset volume
                    str(random.randint(100000, 1000000)),  # Taker buy quote asset volume
                    "0"  # Ignore
                ])
            
            return mock_klines

        try:
            klines = await self.client.get_klines(
                symbol=self.symbol, interval=interval, limit=limit
            )
            return klines
        except Exception as e:
            logger.error(f"Failed to get klines: {e}")
            raise

    async def get_historical_klines(
        self, symbol: str, interval: str, start_time: int, limit: int = 1
    ) -> list:
        """Get historical kline data from specific start time."""
        if not self.client:
            # Mock data for development
            logger.info("Using mock historical kline data for development")
            import random
            from datetime import datetime, timedelta
            
            mock_klines = []
            start_dt = datetime.fromtimestamp(start_time / 1000)
            
            for i in range(limit):
                timestamp = start_dt + timedelta(minutes=i * 30)
                price = 55000 + random.uniform(-2000, 2000)
                
                mock_klines.append([
                    int(timestamp.timestamp() * 1000),  # Open time
                    price,  # Open
                    price + random.uniform(-50, 50),  # High
                    price - random.uniform(-50, 50),  # Low
                    price + random.uniform(-25, 25),  # Close
                    str(random.randint(100, 1000)),  # Volume
                    int(timestamp.timestamp() * 1000),  # Close time
                    str(random.randint(100000, 1000000)),  # Quote asset volume
                    limit,  # Number of trades
                    str(random.randint(100, 1000)),  # Taker buy base asset volume
                    str(random.randint(100000, 1000000)),  # Taker buy quote asset volume
                    "0"  # Ignore
                ])
            
            return mock_klines

        try:
            klines = await self.client.get_klines(
                symbol=symbol, 
                interval=interval, 
                startTime=start_time, 
                limit=limit
            )
            return klines
        except Exception as e:
            logger.error(f"Failed to get historical klines: {e}")
            raise

    async def get_current_price(self) -> float:
        """Get current symbol price."""
        if not self.client:
            # Mock price for development - using realistic BTC price range
            logger.info("Using mock price data for development")
            import random
            # Use realistic BTC price range around $55,000
            return 55000 + random.uniform(-2000, 2000)

        try:
            ticker = await self.client.get_symbol_ticker(symbol=self.symbol)
            return float(ticker["price"])
        except Exception as e:
            logger.error(f"Failed to get current price: {e}")
            raise

    async def start_kline_websocket(self, interval: str = KLINE_INTERVAL_30MINUTE):
        """Start kline WebSocket stream."""
        if not self.client:
            raise RuntimeError("Binance client not initialized")

        self.bsm = BinanceSocketManager(self.client)

        # Start kline socket
        kline_socket = self.bsm.kline_socket(self.symbol, interval=interval)

        async with kline_socket as socket:
            while True:
                try:
                    response = await socket.recv()
                    await self._handle_kline_update(response)
                except Exception as e:
                    logger.error(f"Error in kline WebSocket: {e}")
                    break

    async def _handle_kline_update(self, data: dict[str, Any]):
        """Handle incoming kline data."""
        if "kline_update_callback" in self.callbacks:
            await self.callbacks["kline_update_callback"](data)

    async def get_server_time(self) -> datetime:
        """Get Binance server time."""
        if not self.client:
            raise RuntimeError("Binance client not initialized")

        try:
            server_time = await self.client.get_server_time()
            return datetime.fromtimestamp(server_time["serverTime"] / 1000)
        except Exception as e:
            logger.error(f"Failed to get server time: {e}")
            raise

    async def get_account_info(self) -> dict[str, Any]:
        """Get account information."""
        if not self.client:
            raise RuntimeError("Binance client not initialized")

        try:
            account = await self.client.get_account()
            return account
        except Exception as e:
            logger.error(f"Failed to get account info: {e}")
            raise

    async def get_asset_balance(self, asset: str = "USDT") -> dict[str, Any]:
        """Get asset balance."""
        if not self.client:
            raise RuntimeError("Binance client not initialized")

        try:
            balance = await self.client.get_asset_balance(asset=asset)
            return balance
        except Exception as e:
            logger.error(f"Failed to get asset balance: {e}")
            raise


# Global Binance client instance
binance_client = BinanceClient()
