#!/usr/bin/env python3
"""
检查和处理重复的量化交易进程
识别并安全关闭重复运行的进程
"""

import subprocess
import psutil
import os
import signal
import time
from datetime import datetime
import logging
import json

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class ProcessManager:
    """进程管理器"""
    
    def __init__(self):
        self.trading_keywords = [
            'main.py',
            'signal_generator',
            'settlement_checker',
            'trading_system',
            'quant',
            'binance',
            'scheduler',
            'cron'
        ]
        self.current_dir = os.getcwd()
    
    def get_all_processes(self):
        """获取所有进程信息"""
        processes = []
        for proc in psutil.process_iter(['pid', 'name', 'cmdline', 'create_time', 'cwd', 'status']):
            try:
                processes.append(proc.info)
            except (psutil.NoSuchProcess, psutil.AccessDenied):
                continue
        return processes
    
    def find_trading_processes(self):
        """查找量化交易相关进程"""
        all_processes = self.get_all_processes()
        trading_processes = []
        
        for proc in all_processes:
            if not proc['cmdline']:
                continue
                
            cmdline_str = ' '.join(proc['cmdline']).lower()
            
            # 检查是否包含交易相关关键词
            is_trading_process = False
            for keyword in self.trading_keywords:
                if keyword in cmdline_str:
                    is_trading_process = True
                    break
            
            # 检查是否在当前项目目录下运行
            if proc['cwd'] and self.current_dir in str(proc['cwd']):
                is_trading_process = True
            
            if is_trading_process:
                # 获取更详细的进程信息
                try:
                    p = psutil.Process(proc['pid'])
                    proc_info = {
                        'pid': proc['pid'],
                        'name': proc['name'],
                        'cmdline': proc['cmdline'],
                        'create_time': datetime.fromtimestamp(proc['create_time']),
                        'cwd': proc['cwd'],
                        'status': proc['status'],
                        'cpu_percent': p.cpu_percent(),
                        'memory_info': p.memory_info()._asdict(),
                        'connections': []
                    }
                    
                    # 获取网络连接信息
                    try:
                        connections = p.connections()
                        proc_info['connections'] = [
                            {'laddr': conn.laddr, 'raddr': conn.raddr, 'status': conn.status}
                            for conn in connections if conn.laddr
                        ]
                    except (psutil.AccessDenied, psutil.NoSuchProcess):
                        pass
                    
                    trading_processes.append(proc_info)
                    
                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    continue
        
        return trading_processes
    
    def analyze_duplicate_processes(self, processes):
        """分析重复进程"""
        # 按命令行分组
        process_groups = {}
        
        for proc in processes:
            cmdline_key = ' '.join(proc['cmdline'])
            if cmdline_key not in process_groups:
                process_groups[cmdline_key] = []
            process_groups[cmdline_key].append(proc)
        
        duplicates = []
        for cmdline, procs in process_groups.items():
            if len(procs) > 1:
                # 按创建时间排序，最早的是主进程
                procs.sort(key=lambda x: x['create_time'])
                duplicates.append({
                    'cmdline': cmdline,
                    'main_process': procs[0],
                    'duplicate_processes': procs[1:]
                })
        
        return duplicates
    
    def display_process_info(self, processes):
        """显示进程信息"""
        print("\n" + "="*120)
        print("🔍 量化交易相关进程检查结果")
        print("="*120)
        
        if not processes:
            print("✅ 未发现量化交易相关进程")
            return
        
        print(f"发现 {len(processes)} 个量化交易相关进程:")
        print()
        
        # 表头
        header = f"{'PID':<8} {'进程名':<20} {'状态':<10} {'创建时间':<20} {'CPU%':<8} {'内存MB':<10} {'命令行':<50}"
        print(header)
        print("-" * 120)
        
        for proc in processes:
            memory_mb = proc['memory_info']['rss'] / 1024 / 1024
            cmdline_short = ' '.join(proc['cmdline'])[:50] + '...' if len(' '.join(proc['cmdline'])) > 50 else ' '.join(proc['cmdline'])
            
            row = f"{proc['pid']:<8} {proc['name']:<20} {proc['status']:<10} {proc['create_time'].strftime('%Y-%m-%d %H:%M:%S'):<20} {proc['cpu_percent']:<8.1f} {memory_mb:<10.1f} {cmdline_short:<50}"
            print(row)
            
            # 显示网络连接
            if proc['connections']:
                for conn in proc['connections'][:3]:  # 只显示前3个连接
                    print(f"         └─ 连接: {conn['laddr']} -> {conn.get('raddr', 'N/A')} ({conn['status']})")
        
        print()
    
    def display_duplicate_analysis(self, duplicates):
        """显示重复进程分析"""
        if not duplicates:
            print("✅ 未发现重复进程")
            return
        
        print("\n" + "="*120)
        print("⚠️ 重复进程分析")
        print("="*120)
        
        for i, dup in enumerate(duplicates, 1):
            print(f"\n重复组 {i}:")
            print(f"命令行: {dup['cmdline']}")
            
            main_proc = dup['main_process']
            print(f"  🟢 主进程 (最早启动): PID {main_proc['pid']}, 创建时间: {main_proc['create_time']}")
            
            print(f"  🔴 重复进程 ({len(dup['duplicate_processes'])} 个):")
            for proc in dup['duplicate_processes']:
                print(f"     - PID {proc['pid']}, 创建时间: {proc['create_time']}")
    
    def safe_kill_process(self, pid, process_name):
        """安全关闭进程"""
        try:
            proc = psutil.Process(pid)
            
            # 首先尝试优雅关闭 (SIGTERM)
            logger.info(f"尝试优雅关闭进程 {pid} ({process_name})")
            proc.terminate()
            
            # 等待进程关闭
            try:
                proc.wait(timeout=10)
                logger.info(f"✅ 进程 {pid} 已优雅关闭")
                return True
            except psutil.TimeoutExpired:
                # 如果优雅关闭失败，强制关闭
                logger.warning(f"优雅关闭超时，强制关闭进程 {pid}")
                proc.kill()
                proc.wait(timeout=5)
                logger.info(f"✅ 进程 {pid} 已强制关闭")
                return True
                
        except psutil.NoSuchProcess:
            logger.info(f"进程 {pid} 已不存在")
            return True
        except psutil.AccessDenied:
            logger.error(f"❌ 无权限关闭进程 {pid}")
            return False
        except Exception as e:
            logger.error(f"❌ 关闭进程 {pid} 失败: {e}")
            return False
    
    def kill_duplicate_processes(self, duplicates, confirm=True):
        """关闭重复进程"""
        if not duplicates:
            print("✅ 无重复进程需要关闭")
            return
        
        total_to_kill = sum(len(dup['duplicate_processes']) for dup in duplicates)
        
        if confirm:
            print(f"\n⚠️ 准备关闭 {total_to_kill} 个重复进程")
            response = input("确认继续? (y/N): ").strip().lower()
            if response != 'y':
                print("操作已取消")
                return
        
        killed_count = 0
        failed_count = 0
        
        for dup in duplicates:
            print(f"\n处理重复组: {dup['cmdline'][:50]}...")
            
            for proc in dup['duplicate_processes']:
                success = self.safe_kill_process(proc['pid'], proc['name'])
                if success:
                    killed_count += 1
                else:
                    failed_count += 1
        
        print(f"\n📊 处理结果:")
        print(f"✅ 成功关闭: {killed_count} 个进程")
        if failed_count > 0:
            print(f"❌ 关闭失败: {failed_count} 个进程")
    
    def monitor_processes(self, duration=300):
        """监控进程状态"""
        print(f"\n🔍 开始监控进程状态 ({duration}秒)...")
        
        start_time = time.time()
        check_interval = 30  # 每30秒检查一次
        
        while time.time() - start_time < duration:
            current_processes = self.find_trading_processes()
            duplicates = self.analyze_duplicate_processes(current_processes)
            
            timestamp = datetime.now().strftime('%H:%M:%S')
            print(f"[{timestamp}] 发现 {len(current_processes)} 个交易进程", end="")
            
            if duplicates:
                total_duplicates = sum(len(dup['duplicate_processes']) for dup in duplicates)
                print(f", 其中 {total_duplicates} 个重复进程 ⚠️")
            else:
                print(" ✅")
            
            time.sleep(check_interval)
        
        print("监控完成")

def main():
    """主函数"""
    print("🔍 量化交易进程检查和管理工具")
    print("="*50)
    
    pm = ProcessManager()
    
    # 1. 检查当前进程
    print("\n步骤1: 检查运行中的量化交易进程...")
    processes = pm.find_trading_processes()
    pm.display_process_info(processes)
    
    # 2. 分析重复进程
    print("\n步骤2: 分析重复进程...")
    duplicates = pm.analyze_duplicate_processes(processes)
    pm.display_duplicate_analysis(duplicates)
    
    # 3. 处理重复进程
    if duplicates:
        print("\n步骤3: 处理重复进程...")
        pm.kill_duplicate_processes(duplicates)
        
        # 4. 验证结果
        print("\n步骤4: 验证处理结果...")
        time.sleep(2)  # 等待进程完全关闭
        
        new_processes = pm.find_trading_processes()
        new_duplicates = pm.analyze_duplicate_processes(new_processes)
        
        print("处理后的进程状态:")
        pm.display_process_info(new_processes)
        pm.display_duplicate_analysis(new_duplicates)
        
        # 5. 监控一段时间
        monitor = input("\n是否监控进程状态5分钟? (y/N): ").strip().lower()
        if monitor == 'y':
            pm.monitor_processes(300)
    
    print("\n✅ 进程检查和管理完成!")

if __name__ == "__main__":
    main()
