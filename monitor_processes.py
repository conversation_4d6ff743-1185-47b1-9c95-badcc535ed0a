#!/usr/bin/env python3
"""
进程监控脚本 - 持续监控重复进程
"""

import psutil
import time
from datetime import datetime
import logging

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def monitor_trading_processes():
    """监控交易进程"""
    while True:
        try:
            # 查找main.py进程
            main_processes = []
            for proc in psutil.process_iter(['pid', 'name', 'cmdline', 'create_time']):
                try:
                    if proc.info['name'] == 'Python' and proc.info['cmdline']:
                        cmdline = ' '.join(proc.info['cmdline'])
                        if 'main.py' in cmdline:
                            main_processes.append({
                                'pid': proc.info['pid'],
                                'create_time': datetime.fromtimestamp(proc.info['create_time'])
                            })
                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    continue
            
            timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            
            if len(main_processes) > 1:
                logger.warning(f"[{timestamp}] ⚠️ 发现 {len(main_processes)} 个main.py进程!")
                for proc in main_processes:
                    logger.warning(f"  PID {proc['pid']}, 启动时间: {proc['create_time']}")
            elif len(main_processes) == 1:
                logger.info(f"[{timestamp}] ✅ 正常运行 1 个main.py进程 (PID {main_processes[0]['pid']})")
            else:
                logger.info(f"[{timestamp}] ℹ️ 未发现main.py进程运行")
            
            time.sleep(60)  # 每分钟检查一次
            
        except KeyboardInterrupt:
            logger.info("监控已停止")
            break
        except Exception as e:
            logger.error(f"监控过程中发生错误: {e}")
            time.sleep(60)

if __name__ == "__main__":
    monitor_trading_processes()
