#!/usr/bin/env python3
"""
Risk Manager Reset Script

Resets the trading suspension state to restore normal signal generation.
"""

import asyncio
import sys
from pathlib import Path

# Add the project root to the path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from quant.risk_manager import risk_manager
from quant.utils.logger import get_logger

logger = get_logger(__name__)

async def reset_risk_manager():
    """Reset the risk manager to restore trading functionality."""
    try:
        logger.info("Resetting risk manager...")
        
        # Reset suspension state
        risk_manager.reset_suspension("Manual reset to restore 30-minute signal generation")
        
        # Log current state
        risk_report = risk_manager.get_risk_report()
        logger.info(f"Risk status after reset: {risk_report['risk_status']}")
        logger.info(f"Account balance: ${risk_report['account_state']['balance_usdt']:.2f}")
        logger.info(f"Daily P&L: ${risk_report['account_state']['daily_pnl']:.2f}")
        logger.info(f"Consecutive losses: {risk_report['account_state']['consecutive_losses']}")
        
        logger.info("✅ Risk manager reset successfully!")
        logger.info("🔄 30-minute signal generation should now work normally.")
        
        return True
        
    except Exception as e:
        logger.error(f"Error resetting risk manager: {e}")
        return False

if __name__ == "__main__":
    asyncio.run(reset_risk_manager())