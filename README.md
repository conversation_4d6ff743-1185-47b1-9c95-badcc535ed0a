# HertelQuant Documentation



------

## 一、简单介绍

`HertelQuant`是一套使用python语言开发的基于多线程的异步事件驱动量化交易框架，被设计为适用于中、低、高频等各种业务场景，包含`RestApi、Websocket`实现的行情模块、订单模块、持仓模块、资产模块，此外风控通知模块、数据存储模块、历史回测模块、日志模块能够帮助交易者快速实现完善而高效的量化交易系统，使用者只需将时间精力专注于策略开发层面。



### 1.关键特性:

根据内部开发团队所进行的测试和用户反馈估算得出

+   **高效**： 提高策略开发速度约 300％ 至 500％。
+   **智能**：极佳的编辑器支持。处处皆可自动补全，减少调试时间。
+   **简单**：设计的易于使用和学习，阅读文档的时间更短。
+   **健壮**：生产可用级别的策略代码，可与 **Go语言** 比肩的严谨性。
+   **标准化**：各个交易所的使用方式、数据结构完全统一，一次开发策略，无缝切换不同交易所。



### 2.依赖

Python 3.7及更高版本（Windows操作系统建议使用python3.7，Linux及Mac os可以使用python3.7及更高的版本）

HertelQuant 站在以下巨人的肩膀之上：

-   `requests`负责 http 部分。
-   `websocket-client`负责websocket部分。
-   `pymongo`负责MongoDB数据库部分。



### 3.安装

```
python setup.py install
```



------

## 二、异步代码

多线程是一种内核态内的上下文切换技术。简而言之，其实就是通过多个线程实现代码块相互切换执行。

多线程一般应用在有IO操作的程序中，因为多线程可以利用IO等待的时间去执行一些其他的代码，从而提升代码执行效率。

### 1. IO操作

`IO操作`通常是指相对“慢”的I/O操作（与CPU和内存的处理速度相比），例如等待：

+   通过网络发送的来自客户端的数据
+   客户端接收的你的程序通过网络发送的数据
+   系统要读取并提供给程序的磁盘中的文件的内容
+   你的程序提交给系统写入磁盘的内容
+   远程 API 操作
+   要完成的数据库操作
+   数据库查询返回的结果
+   诸如此类



### 2. 示例（下载多张图片）

建议通过这个简单的示例来体会一下异步编程，下载下来的图片都是高清美女大图哦～

```python
import os
import time
import requests
import threading
import aiohttp
import asyncio


# 图片下载地址列表
url_list = [
    "https://imgs.wantubizhi.com/upload/i_1/T1huMFdJZ3Y2V3VzVloxUEtCaExYZz09/742922166x2007629551_26_0.jpg",
    "https://imgs.wantubizhi.com/upload/i_0/T1huMFdJZ3Y2V3VzVloxUEtCaExYZz09/2494850265x2287407166_26_0.jpg",
    "https://imgs.wantubizhi.com/upload/i_2/T1huMFdJZ3Y2V3VzVloxUEtCaExYZz09/2453434156x3275081135_26_0.jpg"
]


"""在当前目录下创建文件夹用于存放图片"""
if not os.path.exists("pictures"):
    os.makedirs("pictures")

# """单线程"""
# start_time = time.time()
# with requests.session() as session:
#     for url in url_list:
#         name = url.split("/")[-1]   # 切取url中的信息作为图片的名称
#         resp = session.get(url).content
#         with open(file="./pictures/{name}".format(name=name), mode="wb") as file:
#             file.write(resp)
# print("共计用时：{}".format(time.time() - start_time))

# """多线程"""
# def download(session, url):
#     name = url.split("/")[-1]  # 切取url中的信息作为图片的名称
#     resp = session.get(url).content
#     with open(file="./pictures/{name}".format(name=name), mode="wb") as file:
#         file.write(resp)
#
#
# with requests.session() as session:
#     start_time = time.time()
#     for url in url_list:
#         threading.Thread(target=download, args=(session, url, )).start()
# time.sleep(1)
# print("共计用时：{}".format(time.time() - start_time))
```



------

## 三、定时调度和事件驱动

通常我们编写的策略都属于定时调度的策略，也就是“轮询”策略，每隔一定的时间间隔就获取行情数据然后根据行情数据产生策略信号再进行买卖，它的优点是简单、直观，易于上手，其缺点是效率低，尤其是在行情瞬息万变的高波动性的市场上，无法及时跟踪行情的迅速变化。而事件驱动这样的设计思想，上手较为困难，但它取代了轮询式的特点，直接利用监听的方式，来推动业务的流转，其最大的特点就是实时性，非常适合高波动性的市场，其实现是借助于websocket由服务端向客户端主动推送数据从而驱动策略中定义的回调函数异步执行策略代码。



------

## 四、项目结构

开发策略项目时所需构建的项目结构十分简洁：

```
.
├── config.json
├── main.py
└── requirements.txt
```

+   config.json：json格式的项目配置文件
+   main.py：策略的代码文件
+   requirements.txt：项目依赖文件，用于部署至服务器上时快速或自动安装所需三方依赖



------

## 五、配置文件

```json
{
    "LOG": {
        "level": "info",
        "path": "./logs",
        "name": "error.log",
        "console": true,
        "backup_count": 0,
        "clear": false
    },
    "HEARTBEAT": {
        "interval": 2
    },
    "PROXY": "127.0.0.1:9999"
}
```

框架启动的时候，需要指定一个 `json` 格式的配置文件。

### 1.系统配置参数

>   所有系统配置参数均为 `大写字母` 为key;  
>   所有系统配置参数均为 `可选`;  



##### 1.1 LOG

日志配置。包含如下配置：

**示例**:

```json
{
    "LOG": {
        "level": "info",
        "path": "./logs",
        "name": "error.log",
        "console": true,
        "backup_count": 0,
        "clear": false
    }
}
```

**配置说明**:

- console `boolean` 是否打印到控制台，`true 打印到控制台` / `false 打印到文件`，可选，默认为 `true`
- level `string` 日志打印级别 `debug`/ `info`，可选，默认为 `debug`
- path `string` 日志存储路径，可选，默认为 `./logs`
- name `string` 日志文件名，可选，默认为 `error.log`
- clear `boolean` 初始化的时候，是否清理之前的日志文件，`true 清理` / `false 不清理`，可选，默认为 `false`
- backup_count `int` 保存按天分割的日志文件个数，默认0为永久保存所有日志文件，可选，默认为 `0`


##### 1.2 HEARTBEAT

服务心跳配置。

**示例**:

```json
{
    "HEARTBEAT": {
        "interval": 3
    }
}
```

**配置说明**:

- interval `int` 心跳打印时间间隔(秒)，0为不打印 `可选，默认为0`


##### 1.3 PROXY

HTTP代理配置。
大部分交易所在国内访问都需要翻墙，所以在国内环境需要配置HTTP代理。

**示例**:

```json
{
    "PROXY": "127.0.0.1:9999"
}
```

**配置说明**:

- PROXY `string` http代理，解决翻墙问题

> 注意: 此配置为全局配置，将作用到任何HTTP请求，包括Websocket；

##### 1.4 PLATFORMS

交易所账户配置。

**示例**：

```json
{
  "PLATFORMS": {
    "binance": {
      "access_key": "DkxENOG1PI6hHeXMq6uGKjAvfCRE0QfIVpP3Vqozzuks4MPQGn2IFOrpMXGXS",
      "secret_key": "uidORSYusZ8f7BP3c0H1hKwBrTUGrK7k2Ag2KojpIRUvWkwyvljD8AkY2ciXP"
    },
    "okx": {
      "access_key": "6b55f388-7871-4927-bffc-3a35b884500e",
      "secret_key": "443294C5E3C1CDE6945ED5HG95D48E9C",
      "passphrase": "qwer123456"
    }
  }
}
```

**配置说明**:

- binance `str` 交易所名称
    - `access_key str 账户的access_key`
    - `secret_key str 账户的secret_key`
    - `passphrase str 账户的passphrase`，仅OKX需要

##### 1.5 MongoDB

MongoDB数据库配置。

**示例**：

```json
{
  "MONGODB": {
    "host": "**************",
    "port": 27017,
    "username": "admin",
    "password": "123456"
  }
}
```

**配置说明**:

- `host str` `主机地址`，本机则为`127.0.0.1`
- `port int` 端口，默认为`27017`
- `username str` 用户名
- `password str` 密码

##### 1.6 DINGTALK

钉钉webhook token配置。

**示例**：

```json
{
  "DINGTALK": "https://oapi.dingtalk.com/robot/send?access_token=53210ad8ef0f760501afb059056704211f8c8ba12345678eb619784b8d2ae628"
}
```



### 2.自定义配置参数

所有 `config.json` 配置文件里的 `key-value` 格式数据，都可以通过如下方式使用：

```json
{
    "name": "garyhertel"
}
```



```python
from quant.config import config

config.name  # 使用配置里的name字段
```



------

## 六、导入模块

虽然这种导入方式不是最佳的方式，但它却是使用起来最简单的方式：

```python
from quant.entrance import *
```

这条语句实际上导入了我们所需要的所有模块，其等同于：

```python
from quant import const
from quant.asset import Asset
from quant.config import config
from quant.event import Event
from quant.market import Kline, Orderbook, Trade, Ticker
from quant.order import Order
from quant.position import Position
from quant.quant import Quant
from quant.trade import RestApi
from quant.utils import logger, storage, tools
from quant.utils.decorator import method_locker
from quant.utils.dingtalk import Dingtalk
from quant.utils.http_client import HttpRequests
```

导入之后，我们就可以快速直接地使用了。



------

## 七、行情数据

我们只提供了常用的行情数据接口，并且对`ticker`、`orderbook`、`trade`、`kline`这3种高频使用的行情数据在底层进行了专门的处理，以方便使用。因为行情数据属于公共接口，因此可以自行查阅交易所的api文档即可获取任意所需的行情数据。

### 1. 交易所所有产品信息

我们先来看一个获取**交易所所有产品信息**的示例：

```python
from quant.entrance import *


class Strategy:

    def __init__(self):
        self.rest_api = RestApi(
            platform=const.BINANCE_SWAP,
            symbol="BTC/USDT"
        )

        self.do_action()

    def do_action(self):
        success, error = self.rest_api.get_exchange_info()
        if error:
            logger.error("error:", error, caller=self)
            return
        logger.info("success:", success, caller=self)


if __name__ == '__main__':

    Quant.start("config.json", Strategy)
```

执行结果是：

```
[I] [2021-10-07 14:55:13,231] [-] [Quant.start] start io loop ... 
[I] [2021-10-07 14:55:13,988] [-] [Strategy.do_action] success: [{'currency': 'AGLD', 'delisted': False, 'withdraw_disabled': False, 'withdraw_delayed': False, 'deposit_disabled': False, 'trade_disabled': False},
……
```

从这个简单的示例中，我们可以看到：

+   首先我们导入了所需要的模块。
+   然后定义了一个`Strategy`类，在其构造方法中初始化了`rest_api`。
+   定义了一个函数`do_action()`。
+   `get_exchange_info()`这个函数的返回值是一个元组，包含`success`和`error`两个返回值（这使得我们在策略编写过程中将体会到go语言对于错误返回值的处理所带来的繁琐性和严谨性）。
+   程序的入口函数处我们使用`Quant.start("config.json", Strategy)`来启动框架执行策略，底层框架会进行自动加载配置文件和初始化日志设置等一系列操作。
+   启动框架时日志会打印`[I] [2021-10-07 14:55:13,231] [-] [Quant.start] start io loop ... `，其中`[I]`是日志的级别，`[2021-10-07 14:55:13,231]`是datetime时间，`[Quant.start]`是当前类名和函数名称（得益于`caller=self`）,`start io loop ... `是日志内容，说明框架已启动。

### 2.资金费率

Note:	`仅永续合约`.

```python
    def do_action(self):
        # success, error = self.rest_api.get_funding_rate()	# 资金费率
        success, error = self.rest_api.get_history_funding_rate()	# 历史资金费率
        if error:
            logger.error("error:", error, caller=self)
            return
        logger.info("success:", success, caller=self)
```

### 3.订单簿数据

`orderbook`数据作为最常用的行情数据之一，我们对其进行了专门的处理，以便你不再需要自己去耗费大量时间处理这些数据，尤其是考虑到不同交易所所返回的数据是不同的。

我们使用`dataclass`来对`orderbook`数据进行了处理，而`dataclass`是适用于存储数据对象（data object）的python类。

```python
from dataclasses import dataclass
from typing import List, Any, Union


@dataclass
class Orderbook:
    """订单薄"""
        
    platform: str       # 交易平台
    symbol: str         # 交易对
    asks: List[list]    # 买盘数据 [[price, quantity], [...], ...]
    bids: List[list]    # 卖盘数据 [[price, quantity], [...], ...]
    timestamp: int      # 时间戳(毫秒)
    orderbooks: Any     # 原始数据
```

如上所示，订单簿数据的数据结构非常清晰，字段和类型都一目了然，相信这些预处理好的数据将会帮助你更好、更快地开发策略。

也许你会想知道预处理过的数据是不是正确的，或者说你所需的有些数据并不包含其中，为了解决这种顾虑，`orderbook`数据里还返回了一个`orderbooks`，它就是交易所返回的原始数据，你可以检查它并且验证它，或者进行任何你需要的处理。不仅仅是`orderbook`数据，`trade`和`kline`数据也有`trades`和`klines`，它们都是交易所返回的原始数据。

那么，在此基础上，我们要使用获取到的订单簿数据就变的非常容易了：

```python
    def do_action(self):
        orderbook, error = self.rest_api.orderbook()
        if error:
            logger.error("error:", error, caller=self)
            return
    
        price = (orderbook.asks[0][0] + orderbook.asks[1][0]) / 2
        logger.info("price:", price, caller=self)
    
        logger.info("orderbook:", orderbook, caller=self)
```

在这个例子中，我们是计算卖一和卖二的平均价格并且打印出来，可以看到，这里甚至都不需要进行任何类型转换操作。而且在调用`orderbook.asks`时编辑器会自动提示补全，是不是很棒？



除了使用`restapi`获取订单簿数据，我们还可以使用`websocket`订阅订单簿数据，考虑到`restapi`的请求频率限制，交易所其实也推荐我们使用`websocket`来订阅数据，使用`quant`订阅websocket数据也十分简单：

```python
from quant.entrance import *


class Strategy:

    def __init__(self):
        Event(
            platform=const.BINANCE_SWAP,
            channels=[const.ORDERBOOK],
            symbols=["BTC/USDT", "ETH/USDT"],
            orderbook_update_callback=self.on_event_orderbook_update_callback,
        )

    @method_locker("on_event_orderbook_update_callback.lock", wait=False)
    def on_event_orderbook_update_callback(self, orderbook: Orderbook):
        logger.debug("orderbook:", orderbook, caller=self)


if __name__ == '__main__':

    Quant.start("config.json", Strategy)
```

在这个例子中，我们首先是初始化了`Event`，其中需要传入一些参数：

+   platform：交易所名称
+   channels：订阅的数据频道（这里是订阅了订单簿，也可以订阅多个频道）
+   symbols：订阅的币种（可以是多个币种）
+   orderbook_update_callback：订单簿数据更新回调函数

Note:

>   订单簿数据更新回调函数中我们使用的是`logger.debug("orderbook:", orderbook, caller=self)`,日志打印的级别是`debug`，这通常用于本地开发策略时调试，需要将配置文件中的日志配置项中的`level`设置为`debug`才能输出`debug`日志。部署到服务器时，我们通常使用`info`级别的日志输出，这样就不会输出`debug`日志，日志文件不会过大，方便查找和排查问题。
>
>   @method_locker("on_event_orderbook_update_callback.lock", wait=False)我们将在后续讲到“并发和锁”时详细介绍。



### 4.逐笔成交数据

trade数据结构：

```python
@dataclass
class Trade:
    """交易数据"""
        
    platform: str       # 交易平台
    symbol: str         # 交易对
    action: str         # 方向 BUY / SELL
    price: float        # 价格
    quantity: float     # 数量
    timestamp: int      # 时间戳(毫秒)
    trades: Any         # 原始数据
```

获取trade数据：

```python
    def do_action(self):
        trade, error = self.rest_api.trade()
        if error:
            logger.error("error:", error, caller=self)
            return
        logger.info("trade:", trade, caller=self)
```

订阅trade数据：

```python
from quant.entrance import *


class Strategy:

    def __init__(self):
        Event(
            platform=const.BINANCE_SWAP,
            channels=[const.TRADE],
            symbols=["BTC/USDT", "ETH/USDT"],
            trade_update_callback=self.on_event_trade_update_callback,
        )

    @method_locker("on_event_trade_update_callback.lock", wait=False)
    def on_event_trade_update_callback(self, trade: Trade):
        logger.debug("trade:", trade, caller=self)


if __name__ == '__main__':

    Quant.start("config.json", Strategy)
```



### 5. K线数据

```python
@dataclass
class Kline:
    """K线"""
        
    platform: str                       # 交易平台
    symbol: str                         # 交易对
    open: Union[float, list]            # 开盘价
    high: Union[float, list]            # 最高价
    low: Union[float, list]             # 最低价
    close: Union[float, list]           # 收盘价
    volume: Union[float, list]          # 成交量
    timestamp: Union[float, list]       # 时间戳(毫秒)
    interval: str                       # 周期
    klines: Any                         # 原始数据
```

K线数据有一些特别，从这个`Kline`数据类里我们可以看到，其中的`open`开盘价等数据既可以是`float`也可以是`List[float]`，这是因为`restapi`和`websocket`返回的K线数据结构不同而导致的。

`websocket`推送回来的数据是最新的一根k线数据，因此我们的处理和订单簿、逐笔数据是一样的，但是`restapi`返回的K线数据，是最近的**500**根K线数据，因此我们将有些字段处理成了`List[float]`，如此一来，当我们要计算某些技术指标时，比如`MA`，它需要传入收盘价数组，我们只需要`numpy.array(kline.close)`就可以得到收盘价数组了。

K线数据的`data`和`trade`以及`orderbook`不同，`restapi`并非交易所返回的原始数据，因为考虑到实际使用的便捷性，这个字段返回的是经过处理后的统一结构的最近的500条K线数据，其结构是：

```python
[
    [1590451200000, 8887.0, 8995.0, 8702.0, 8841.1, 100837896.0], 
    [1590537600000, 8841.2, 9226.8, 8820.0, 9192.0, 94171464.0], 
    [1590624000000, 9193.1, 9612.5, 9105.0, 9561.9, 103684505.0], 
    [1590710400000, 9562.0, 9580.0, 9330.0, 9416.9, 94890074.0],
    ……
]
```

从时间戳可以看到，是按时间升序排列的，也就是最新的一根K线数据在最后，其负索引是`-1`.



获取K线数据：

```python
    def do_action(self):
        kline, error = self.rest_api.kline(interval=const.KLINE_1M)
        if error:
            logger.error("error:", error, caller=self)
            return
        logger.info("kline:", kline, caller=self)
```

订阅K线数据：

```python
from quant.entrance import *


class Strategy:

    def __init__(self):
        Event(
            platform=const.BINANCE_SWAP,
            channels=[const.KLINE_1M],
            symbols=["BTC/USDT", "ETH/USDT"],
            kline_update_callback=self.on_event_kline_update_callback,
        )

    @method_locker("on_event_kline_update_callback.lock", wait=False)
    def on_event_kline_update_callback(self, kline: Kline):
        logger.debug("kline:", kline, caller=self)


if __name__ == '__main__':

    Quant.start("config.json", Strategy)
```

K线周期枚举（不同交易所支持的周期有差异，因此有些交易所的有些周期可能没有）：

```python
# K线周期
KLINE_1M = "kline_1m"                     # 1分钟k线
KLINE_3M = "kline_3m"                     # 3分钟k线
KLINE_5M = "kline_5m"                     # 5分钟k线
KLINE_15M = "kline_15m"                   # 15分钟k线
KLINE_30M = "kline_30m"                   # 30分钟k线
KLINE_1H = "kline_1h"                     # 1小时k线
KLINE_2H = "kline_2h"                     # 2小时k线
KLINE_4H = "kline_4h"                     # 4小时k线
KLINE_6H = "kline_6h"                     # 6小时k线
KLINE_12H = "kline_12h"                   # 12小时k线
KLINE_1D = "kline_1d"                     # 1日k线
```

在我们使用K线周期时，我们可以直接使用const模块来调用常量，这样可以可以避免一些人为的输入错误。

### 6. ticker数据

ticker数据中包含有买卖盘最优价格极其相应信息，还包含24小时的开高低收成交量等信息，因此其使用频率也是挺高的。

ticker数据结构：

```python
@dataclass
class Ticker:
    """Ticker."""
    
    platform: str           # 平台
    symbol: str             # 交易对
    ask_price: float        # 卖一价格
    ask_quantity: float     # 卖一数量
    bid_price: float        # 买一价格
    bid_quantity: float     # 买一数量
    open: float             # 24小时开盘价
    high: float             # 24小时最高价
    low: float              # 24小时最低价
    close: float            # 最新价格
    volume: float           # 24小时成交量
    turnover: float         # 24小时成交额
    timestamp: int          # 时间戳(毫秒)
    tickers: Any            # 原始数据
```

订阅websocket ticker数据：

```python
from quant.entrance import *


class Strategy:

    def __init__(self):
        Event(
            platform=const.BINANCE_SWAP,
            channels=[const.TICKER],
            symbols=["BTC/USDT", "ETH/USDT"],
            ticker_update_callback=self.on_event_ticker_update_callback,
        )

    @method_locker("on_event_ticker_update_callback.lock", wait=False)
    def on_event_ticker_update_callback(self, ticker: Ticker):
        logger.debug("ticker:", ticker, caller=self)


if __name__ == '__main__':

    Quant.start("config.json", Strategy)
```



------

## 八、私有数据

我们只提供了常用的私有数据查询接口，并且对`order`、`asset`、`position`这3种高频使用的私有数据在底层进行了专门的处理，以方便使用。

### 1.订单

order数据结构:

```python
@dataclass
class Order:
    
    platform: str               # 交易平台
    symbol: str                 # 交易对
    order_no: Union[str, int]   # 委托单号
    action: str                 # 买卖类型
    order_type: str             # 委托单类型
    price: float                # 委托价格
    quantity: float             # 委托数量（限价单）
    filled_qty: float           # 成交数量
    remain: float               # 剩余未成交数量
    status: str                 # 委托单状态
    timestamp: int              # 创建订单时间戳(毫秒)
    avg_price: float            # 成交均价
    fee: float                  # 手续费
    utime: int                  # 交易所订单更新时间
    orders: Any                 # 原始数据
```

获取order信息：

```python
    def do_action(self):
        order, error = self.rest_api.order(order_no="1222")
        if error:
            logger.error("error:", error, caller=self)
            return
        logger.info("order:", order, caller=self)
```

订阅order数据：

```python
from quant.entrance import *


class Strategy:

    def __init__(self):
        Event(
            platform=const.BINANCE_SWAP,
            channels=[const.ORDER],
            symbols=["BTC/USDT", "ETH/USDT"],
            order_update_callback=self.on_event_order_update_callback,
        )

    @method_locker("on_event_order_update_callback.lock", wait=True)
    def on_event_order_update_callback(self, order: Order):
        logger.info("order:", order, caller=self)


if __name__ == '__main__':

    Quant.start("config.json", Strategy)
```



订单数据中相关字段枚举：

```python
# 订单动作
BUY = "buy"                         # 买入开多
SELL = "sell"                       # 卖出平多
BUY_TO_COVER = "buy_to_cover"       # 买入平空
SELL_SHORT = "sell_short"           # 卖出开空

# 订单类型
LIMIT = "limit"                     # 现价订单
MARKET = "market"                   # 市价订单
POST_ONLY = "post_only"             # 只做maker单

# 订单状态
UNKNOWN = "unknown"                 # 未知状态
SUBMITTED = "submitted"             # 已提交（待成交）
PARTIAL_FILLED = "partial_filled"   # 部分成交
FILLED = "filled"                   # 完全成交
CANCELED = "canceled"               # 已撤销
FAILED = "failed"                   # 失败（已过期）
```

### 2.资产

资产数据结构：

```python
@dataclass
class Asset:
    
    platform: str       # 交易平台
    currency: str       # 资产名称
    total: float        # 总余额
    locked: float       # 冻结余额
    free: float         # 可用余额
    timestamp: int      # 毫秒时间戳
    assets: Any         # 原始数据
```

获取资产数据：

```python
    def do_action(self):
        asset, error = self.rest_api.asset(currency="BTC")
        if error:
            logger.error("error:", error, caller=self)
            return
        logger.info("asset:", asset, caller=self)
```

订阅资产更新：

```python
from quant.entrance import *


class Strategy:

    def __init__(self):
        Event(
            platform=const.BINANCE_SWAP,
            channels=[const.ASSET],
            symbols=["BTC/USDT", "ETH/USDT"],
            order_update_callback=self.on_event_asset_update_callback,
        )

    @method_locker("on_event_asset_update_callback.lock", wait=True)
    def on_event_asset_update_callback(self, asset: Asset):
        logger.info("asset:", asset, caller=self)


if __name__ == '__main__':

    Quant.start("config.json", Strategy)
```

### 3.持仓(仅合约)

持仓数据结构：

```python
@dataclass
class Position:
    """持仓数据.
    Note:   有些交易所websockets推送持仓时只推送变化的那个方向的仓位，如果是双向持仓，也就是说只推送一个方向的，
            在这种情况下，没有推送的方向我们处理为返回None，因此策略层面可以做一些处理来应对这种情况，例如：
            `
            if position.long_quantity is not None:
                long_quantity = position.long_quantity
                long_avg_price = position.long_avg_price
            if position.short_quantity is not None:
                short_quantity = position.short_quantity
                short_avg_price = position.short_avg_price
            `

    """
    platform: str               # 交易平台
    symbol: str                 # 交易对 如: ETH/BTC
    long_quantity: float        # 多仓数量
    long_avg_price: float       # 多仓平均价格
    short_quantity: float       # 空仓数量
    short_avg_price: float      # 空仓平均价格
    utime: int                  # 交易所订单更新时间
    positions: Any              # 原始数据
```

获取持仓：

```python
    def do_action(self):
        position, error = self.rest_api.position()
        if error:
            logger.error("error:", error, caller=self)
            return
        logger.info("position:", position, caller=self)
```

订阅持仓：

```python
from quant.entrance import *


class Strategy:

    def __init__(self):
        Event(
            platform=const.BINANCE_SWAP,
            channels=[const.POSITION],
            symbols=["BTC/USDT", "ETH/USDT"],
            order_update_callback=self.on_event_position_update_callback,
        )

    @method_locker("on_event_position_update_callback.lock", wait=True)
    def on_event_position_update_callback(self, position: Position):
        logger.info("position:", position, caller=self)


if __name__ == '__main__':

    Quant.start("config.json", Strategy)
```

------

## 九、交易模块

### 1. 买入开多

```python
    def do_action(self):
        # 现价订单，不传order_type则默认为限价订单
        order_id, error = self.rest_api.buy(
            price=1, 
            quantity=1, 
            order_type=const.LIMIT
        )
        if error:
            logger.error("error:", error, caller=self)
            return
        logger.info("order_id:", order_id, caller=self)

        # 市价订单
        order_id, error = self.rest_api.buy(
            price=None,
            quantity=1,
            order_type=const.MARKET
        )
        if error:
            logger.error("error:", error, caller=self)
            return
        logger.info("order_id:", order_id, caller=self)

        # 只做maker单
        order_id, error = self.rest_api.buy(
            price=1,
            quantity=1,     # 现货市价买入订单是指下单金额，而非数量
            order_type=const.POST_ONLY
        )
        if error:
            logger.error("error:", error, caller=self)
            return
        logger.info("order_id:", order_id, caller=self)
```

### 2. 卖出平多

```python
def do_action(self):
    # 现价订单，不传order_type则默认为限价订单
    order_id, error = self.rest_api.sell(
        price=1,
        quantity=1,
        order_type=const.LIMIT
    )
    if error:
        logger.error("error:", error, caller=self)
        return
    logger.info("order_id:", order_id, caller=self)

    # 市价订单
    order_id, error = self.rest_api.sell(
        price=None,
        quantity=1,
        order_type=const.MARKET
    )
    if error:
        logger.error("error:", error, caller=self)
        return
    logger.info("order_id:", order_id, caller=self)

    # 只做maker单
    order_id, error = self.rest_api.sell(
        price=1,
        quantity=1,     # 现货市价卖出订单是数量
        order_type=const.POST_ONLY
    )
    if error:
        logger.error("error:", error, caller=self)
        return
    logger.info("order_id:", order_id, caller=self)
```

### 3.卖出开空 

Note: 仅合约支持。

```python
    def do_action(self):
        # 现价订单，不传order_type则默认为限价订单
        order_id, error = self.rest_api.sell_short(
            price=1,
            quantity=1,
            order_type=const.LIMIT
        )
        if error:
            logger.error("error:", error, caller=self)
            return
        logger.info("order_id:", order_id, caller=self)

        # 市价订单
        order_id, error = self.rest_api.sell_short(
            price=None,
            quantity=1,
            order_type=const.MARKET
        )
        if error:
            logger.error("error:", error, caller=self)
            return
        logger.info("order_id:", order_id, caller=self)

        # 只做maker单
        order_id, error = self.rest_api.sell_short(
            price=1,
            quantity=1,     
            order_type=const.POST_ONLY
        )
        if error:
            logger.error("error:", error, caller=self)
            return
        logger.info("order_id:", order_id, caller=self)
```

### 4.买入平空

Note: 仅合约支持。

```python
def do_action(self):
    # 现价订单，不传order_type则默认为限价订单
    order_id, error = self.rest_api.buy_to_cover(
        price=1,
        quantity=1,
        order_type=const.LIMIT
    )
    if error:
        logger.error("error:", error, caller=self)
        return
    logger.info("order_id:", order_id, caller=self)

    # 市价订单
    order_id, error = self.rest_api.buy_to_cover(
        price=None,
        quantity=1,
        order_type=const.MARKET
    )
    if error:
        logger.error("error:", error, caller=self)
        return
    logger.info("order_id:", order_id, caller=self)

    # 只做maker单
    order_id, error = self.rest_api.buy_to_cover(
        price=1,
        quantity=1,
        order_type=const.POST_ONLY
    )
    if error:
        logger.error("error:", error, caller=self)
        return
    logger.info("order_id:", order_id, caller=self)
```

### 5. 撤销单个订单

```python
    def do_action(self):
        order_id, error = self.rest_api.revoke_order(order_no="122332")
        if error:
            logger.error("revoke order error:", error, caller=self)
            return
        logger.info("revoke order success:", order_id, caller=self)
```

### 6. 撤销多个订单

```python
    def do_action(self):
        success, error = self.rest_api.revoke_orders(order_nos=["122332", "3332222"])
        logger.info("revoke orders success:", success, caller=self)
        logger.error("revoke orders error:", error, caller=self)
```

在撤销多个订单时，返回的`success`和`error`都是列表，撤销成功的订单编号在`success`中，撤销失败的订单编号在`error`中。

### 7. 获取全部未成交订单

```python
    def do_action(self):
        open_order_ids, error = self.rest_api.get_open_orders()
        if error:
            logger.error("get open orders error:", error, caller=self)
            return
        logger.info("get open orders success:", open_order_ids, caller=self)
```

如果查询成功，如果当前币对**不存在**未成交委托单，则`success`是一个空的列表，反之，则success是一个包含所有未成交订单的订单编号的列表。

因此，我们可以直接将获取的这个存放有全部未成交订单的列表传给`revoke_orders`这个撤销多个订单的方法，就可以撤销当前币对的全部未成交订单了。

```python
    def do_action(self):
        open_order_ids, error = self.rest_api.get_open_orders()
        if error:
            logger.error("get open orders error:", error, caller=self)
            return
        logger.info("get open orders success:", open_order_ids, caller=self)
        
        success, error = self.rest_api.revoke_orders(order_nos=open_order_ids)
        logger.info("revoke orders success:", success, caller=self)
        logger.error("revoke orders error:", error, caller=self)
```

### 8. 设置杠杆倍数

Note: 仅合约支持。

```python
    def do_action(self):
        success, error = self.rest_api.set_leverage(leverage=10)
        if error:
            logger.error("error:", error, caller=self)
            return
        logger.info("success:", success, caller=self)
```

### 9. 设置全逐仓模式

Note: 仅Binance合约支持。

```python
    def do_action(self):
        # success, error = self.rest_api.set_margin_mode(margin_mode="fixed")     # 逐仓
        success, error = self.rest_api.set_margin_mode(margin_mode="crossed")     # 全仓
        if error:
            logger.error("error:", error, caller=self)
            return
        logger.info("success:", success, caller=self)
```



------

## 十、服务心跳和定时任务调度

### 1. 服务心跳

`HertelQuant`启动后，会每隔1秒执行一次服务心跳，我们可以在配置文件中配置心跳，以便输出服务心跳：

```json
{
  "HEARTBEAT": {
        "interval": 3
    }
}
```

`interval`的值，是指每隔几秒输出一次心跳，如果是1，则每隔1秒输出心跳信息：

```
[I] [2022-05-19 15:51:41,335] [HeartBeat.ticker] do server heartbeat, count: 1 
[I] [2022-05-19 15:51:42,336] [HeartBeat.ticker] do server heartbeat, count: 2 
[I] [2022-05-19 15:51:43,338] [HeartBeat.ticker] do server heartbeat, count: 3 
[I] [2022-05-19 15:51:44,340] [HeartBeat.ticker] do server heartbeat, count: 4 
[I] [2022-05-19 15:51:45,342] [HeartBeat.ticker] do server heartbeat, count: 5 
[I] [2022-05-19 15:51:46,343] [HeartBeat.ticker] do server heartbeat, count: 6 
[I] [2022-05-19 15:51:47,345] [HeartBeat.ticker] do server heartbeat, count: 7 
[I] [2022-05-19 15:51:48,346] [HeartBeat.ticker] do server heartbeat, count: 8 
[I] [2022-05-19 15:51:49,348] [HeartBeat.ticker] do server heartbeat, count: 9 
[I] [2022-05-19 15:51:50,350] [HeartBeat.ticker] do server heartbeat, count: 10 
[I] [2022-05-19 15:51:51,352] [HeartBeat.ticker] do server heartbeat, count: 11 
[I] [2022-05-19 15:51:52,353] [HeartBeat.ticker] do server heartbeat, count: 12 
```

### 2. 定时任务调度

运用每隔一秒打印一次心跳的机制，我们可以创建定时任务：

```python
from quant.entrance import *


class Strategy:

    def __init__(self):
        
        self.rest_api = RestApi(platform=const.BINANCE_SWAP, symbol="BTC/USDT")

        # 创建定时任务，每隔10秒获取一次资产信息。注意此处传入的是函数名称。
        Quant.create_loop_task(10, self.do_action)

    def do_action(self):
        open_order_ids, error = self.rest_api.get_open_orders()
        if error:
            logger.error("get open orders error:", error, caller=self)
            return
        logger.info("get open orders success:", open_order_ids, caller=self)


if __name__ == '__main__':

    Quant.start("config.json", Strategy)
```

这里我们在构造方法中创建了一个定时任务：

```python
Quant.create_loop_task(10, self.do_action)
```

这里其实就是绑定服务心跳，当服务心跳是10秒的整数倍时就会执行一次`do_action`方法。

`HertelQuant`底层其实就创建了很多定时任务，比如定时检查websocket的链接情况等等。

### 3. 取消定时任务

定时任务也是可以取消的：

```python
task_id = Quant.create_loop_task(10, self.do_action)
Quant.cancel_loop_task(task_id=task_id)
```

在我们创建定时任务时，会返回一个`task_id`，然后我们随时都可以使用`Quant.cancel_loop_task()`方法取消这个定时任务。有兴趣的话就试一试吧。

### 4. 延迟调度

我们也可以使用`call_later`方法来延迟调度：

```python
Quant.call_later(10, Quant.stop)
```

十秒后执行`Quant.stop()`方法，就可以停止`HertelQuant`量化交易系统，这在某些时候可能会很有用。再次需要注意的是，我们在调度任务时传入的都是函数或者方法的名称，后面不要带括号哦。

------

## 十一、工具包

我们在开发量化交易策略时，经常需要用到一些涉及到时间戳转化的方法：

```python
tools.get_cur_timestamp()									# 获取当前时间戳（秒）
tools.get_cur_timestamp_ms()								# 获取当前时间戳(毫秒)
tools.get_localtime()										# 获取本地时间
tools.ts_to_datetime_str(ts)								# 将时间戳转换为日期时间格式，年-月-日 时:分:秒
tools.datetime_str_to_ts(dt_str)							# 将日期时间格式字符串转换成时间戳
```

比如，我们封装好的数据类中返回的`timestamp`都是13位的毫秒时间戳，那我们就可以使用`tools.ts_to_datetime_str(ts)`方法来转化为`datetime`时间，使之更具有可读性。

```python
tools.ts_to_datetime_str(trade.timestamp / 1000)
```

要记得先将毫秒时间戳除以1000，即转为秒时间戳之后，再进行转化哦。

------

## 十二、风控通知推送

信息推送对于风控通知来说是至关重要的。`HertelQuant`内置信息推送模块，可直接调用以推送信息至钉钉。

`Note`：需在配置文件中设置钉钉`WebHook token`，建立钉钉群聊后添加一个`WebHook`机器人，创建机器人时指定关键字如`交易`。

### 1. 推送文本类型信息

推送文本类型信息时需包含关键字，否则无法送达。

```python
Dingtalk.text("交易提醒：BTC的价格已达到3万美元！")
```

### 2. 推送`markdown`类型信息

推送`markdown`信息时无需包含关键字。下面看一个持仓变动时推送持仓信息的示例：

```python
@method_locker("on_event_order_update_callback.lock", wait=True)
def on_event_order_update_callback(self, order: Order):
        """订单更新"""
        logger.debug("order:", order, caller=self)

        if order.status == const.FILLED:
            # 推送订单成交通知
            content = "### 订单更新推送\n\n" \
                      "> **策略名称:** {strategy}\n\n" \
                      "> **交易平台:** {platform}\n\n" \
                      "> **交易币对:** {symbol}\n\n" \
                      "> **订单方向:** {action}\n\n" \
                      "> **订单类型:** {order_type}\n\n" \
                      "> **订单编号:** {order_id}\n\n" \
                      "> **订单状态:** {status}\n\n" \
                      "> **剩余数量:** {remain}\n\n" \
                      "> **委托数量:** {quantity}\n\n" \
                      "> **委托均价:** {price}\n\n" \
                      "> **成交数量:** {filled_quantity}\n\n" \
                      "> **成交均价:** {avg_price}\n\n" \
                      "> **手续费用:** {fee}\n\n" \
                      "> **创建时间:** {timestamp}\n\n" \
                      "> **更新时间:** {utime}".format(
                        strategy=config.strategy_name,
                        platform=order.platform,
                        symbol=order.symbol,
                        action=order.action,
                        order_type=order.order_type,
                        order_id=order.order_no,
                        status=order.status,
                        remain=order.remain,
                        quantity=order.quantity,
                        price=order.price,
                        filled_quantity=order.filled_qty,
                        avg_price=order.avg_price,
                        fee=order.fee,
                        timestamp=tools.ts_to_datetime_str(order.timestamp / 1000),
                        utime=tools.ts_to_datetime_str(order.utime / 1000)
            )
            DingTalk.markdown(content)
```

### 3. 推送订单、资产、持仓快照

像上面这样推送订单信息的场景是频繁的，与此类似的还有推送资产和持仓快照，因此对于这三者，`HertelQuant`都是内置支持的，我们只需要直接传入相应的对象即可：

```python
    @method_locker("on_event_order_update_callback.lock", wait=True)
    def on_event_order_update_callback(self, order: Order):
        """订单更新"""
        DingTalk.send_order_message(order=order)

    @method_locker("on_event_asset_update_callback.lock", wait=True)
    def on_event_asset_update_callback(self, asset: Asset):
        DingTalk.send_asset_message(asset=asset)

    @method_locker("on_event_position_update_callback.lock", wait=True)
    def on_event_position_update_callback(self, position: Position):
        DingTalk.send_position_message(position=position)
```



------

## 十三、数据存储

### 1. MongoDB数据库使用注意事项

如果在配置文件中配置了Mongodb数据库，启动框架时会自动连接，连接失败或者授权验证登陆失败，就会报错并停止程序。因此，如果不需要使用数据库，请删除配置文件中的mongodb数据库配置。

```json
{
    "MONGODB": {
        "host": "127.0.0.1",
        "port": 27017,
        "username": "root",
        "password": "qwer"
    }
}
```

启动时如果连接Mongodb数据库成功，会打印一条信息：

```
[I] [2021-05-17 20:16:33,022] create mongodb connection pool. 
```

### 2. 具体的可调用方法

```python
storage.txt_save(content, filename)						# 保存数据至txt文件
storage.txt_read(filename)								# 读取txt文件中的数据
storage.mongodb_save(database, collection, data)		# 保存数据至mongodb
storage.mongodb_read_data(database, collection)			# 读取mongodb数据库中某集合中所有数据，并保存至一个列表中
storage.save_to_csv_file(tuple, path)					# 保存文件至csv文件
storage.read_csv_file(path)								# 读取csv文件中保存的数据
```



------

## 十四、并发与锁

### 1. 行情数据的使用场景

在之前的回调方法上我们用到了`method_locker`，`HertelQuant`是基于多线程的量化交易系统，但既然涉及到并发，就必然要提到锁。

锁的使用，一方面是因为数据的推送频率太高，我们的策略很可能处理不过来，例如在`orderbook`数据回调方法这里：

```python
@method_locker(name="on_event_orderbook_update_callback.lock", wait=False)
def on_event_orderbook_update_callback(self, orderbook: Orderbook):
    logger.debug("orderbook:", orderbook, caller=self)
```

盘口数据的变化非常快，因此其推送频率非常高，约为100ms推送一次，而我们的策略很可能无法处理如此之多的数据，所以我们在orderbook数据的回调方法上加锁，并且指定`wait=False`，这样在锁被锁住时，再推送过来的数据就不会得到处理了，而是直接返回。

### 2. 私有数据的使用场景

然而，在我们处理私有数据时，相信比如说任意一笔订单或者持仓的变动信息对于我们的策略而言都是至关重要的。如果我们不加锁，比如一次性推送3笔订单数据，那它们就是并发处理的，但是这样一来，可能会带来安全问题，比如对于一些共享变量的共用等等。因此我们可以加上锁并指定`wait=True`，使多个订单数据强制进行同步处理，即一个接一个地按照顺序处理。

```python
@method_locker(name="on_event_order_update_callback.lock", wait=True)
def on_event_order_update_callback(self, order: Order):
    """订单更新"""
    logger.debug("order:", order, caller=self)
```

### 3. 不同的锁

`method_locker`的`name`参数，可以通过不同的`name`来创建多把锁，如果`name`一致，就表明使用的是同一把锁。

### 4. 简单理解

并发与锁的使用可能一开始比较难以理解，因此，如果你实在不是很明白也没有关系，就按照我们的策略模版来使用即可。



------

## 十五、策略模板

### 1.`main.py`

```python
"""
Author: Gary-Hertel
Email:  <EMAIL>
Date:   2022-04-08
"""


from quant.entrance import *


class Strategy:

	def __init__(self):
		
		self.restapi = RestApi(platform=const.BINANCE_SWAP, symbol="BTC/USDT")
		Quant.create_single_task(self.do_action)
		
		Event(
			platform=const.BINANCE_SWAP,
			channels=[const.TICKER, const.TRADE, const.ORDERBOOK, const.KLINE_1H, const.ORDER, const.ASSET],
			symbols=["BTC/USDT", "ETH/USDT"],
			ticker_update_callback=self.on_event_ticker_update_callback,
			trade_update_callback=self.on_event_trade_update_callback,
			orderbook_update_callback=self.on_event_orderbook_update_callback,
			kline_update_callback=self.on_event_kline_update_callback,
			order_update_callback=self.on_event_order_update_callback,
			position_update_callback=self.on_event_position_update_callback,
			asset_update_callback=self.on_event_asset_update_callback
		)
		
	def do_action(self):
		trade, _ = self.restapi.trade()
		logger.info("trade:", trade, caller=self)
		
		asset, _ = self.restapi.asset(currency="USDT")
		logger.info("asset:", asset, caller=self)
		
	@method_locker(name="on_event_ticker_update_callback.lock", wait=False)
	def on_event_ticker_update_callback(self, ticker: Ticker):
		logger.debug("ticker:", ticker, caller=self)
	
	@method_locker(name="on_event_trade_update_callback.lock", wait=False)
	def on_event_trade_update_callback(self, trade: Trade):
		logger.debug("trade:", trade, caller=self)

	@method_locker(name="on_event_orderbook_update_callback.lock", wait=False)
	def on_event_orderbook_update_callback(self, orderbook: Orderbook):
		logger.debug("orderbook:", orderbook, caller=self)
	
	@method_locker(name="on_event_kline_update_callback.lock", wait=False)
	def on_event_kline_update_callback(self, kline: Kline):
		logger.info("kline:", kline, caller=self)
	
	@method_locker(name="on_event_order_update_callback.lock", wait=True)
	def on_event_order_update_callback(self, order: Order):
		logger.info("order:", order, caller=self)
	
	@method_locker(name="on_event_asset_update_callback.lock", wait=True)
	def on_event_asset_update_callback(self, asset: Asset):
		logger.info("asset:", asset, caller=self)
	
	@method_locker(name="on_event_position_update_callback.lock", wait=True)
	def on_event_position_update_callback(self, position: Position):
		logger.info("position:", position, caller=self)


if __name__ == '__main__':

	Quant.start("config.json", Strategy)
```

### 2. `config.json`

```json
{
    "LOG": {
        "level": "info",
        "path": "./logs",
        "name": "error.log",
        "console": true,
        "backup_count": 0,
        "clear": false
    },
    "PLATFORMS": {
        "binance": {
            "access_key":"",
            "secret_key":""
        },
        "okx": {
            "access_key": "",
            "secret_key": "",
            "passphrase": ""
        }
    },
    "PROXY": "127.0.0.1:9999",
    "DINGTALK": ""
}
```

### 3. `requirements.txt`

```
websocket-client==0.57.0
pymongo==3.11.3
requests==2.22.0
```



------

## 十六、服务器选购

当我们的策略在本地开发测试好之后，我们就可以进行实盘部署了，这时候我们就需要购买服务器了。为什么要用服务器呢？股票市场呢每逢节假日都是休市的，你也可以用你在办公室工作的电脑在上班的时候摸鱼，比如用命令行去运行你的策略程序，但是呢你办公室可能会断电，也可能会断网，会有很多不稳定因素，那如果遇到这种情况的话可能就会造成策略终止运行，给你的证券账户带来不可预料的损失。比如你设置了一个止损价格，但是你们办公室的网络断了，没有执行止损。所以呢我们还是推荐购买服务器来进行实盘策略的部署。

### 1. 使用服务器部署策略的优点

>   云服务器在性能、成本、灵活性以及工作连续性等方面，都具有不可忽视的优势，租用云服务器既是用户发展的需要，也是云计算技术时代下的趋势。

其实对我们的业务场景来说，主要就是稳定，购买并搭建好环境之后，就可以让它一直运行了，基本上不用去管它，节省了很多时间和精力，如果你还没有使用过，相信你在使用后一定会觉得比使用本地电脑来跑策略好太多。



### 2. 操作系统的选择

我们现实工作生活中，电脑的操作系统基本上都是windows，或者就是mac os，但是服务器呢我们一般是使用linux系统，

>   与Windows, iOS, MacOS这些普遍被运用的操作系统相比，Linux有着自己无可取代的优势：
>
>   -   开源：linux内核的源代码是开放的。
>   -   占用的内存小：因为不需要炫酷的图形界面，Linux可以省下巨大的内存。
>   -   稳定且安全：Linux的主要用户是开发者，并且需要满足服务器稳定的需求，Linux系统的补丁更新很快，也因为开源和内部的安全机制，Linux可以说是一个”百毒不侵“的系统。

内核指的是一个提供设备驱动、文件系统、进程管理、网络通信等功能的系统软件，内核并不是一套完整的操作系统，它只是操作系统的核心。一些组织或厂商将 Linux 内核与各种软件和文档包装起来，并提供系统安装界面和系统配置、设定与管理工具，就构成了 Linux 的发行版本。Linux有许多的发行版，不同版本可以满足用户的特定需求。

Linux 的发行版本可以大体分为两类：

-   商业公司维护的发行版本，以著名的 Red Hat 为代表；
-   社区组织维护的发行版本，以 Debian 为代表。

Ubuntu 基于知名的 Debian Linux 发展而来，界面友好，容易上手，对硬件的支持非常全面，是目前最适合做桌面系统的 Linux 发行版本，而且 Ubuntu 的所有发行版本都免费提供。

我国国内互联网公司常用的 Linux 发行版本是 CentOS ，它是基于 Red Hat Enterprise Linux 源代码重新编译、去除 Red Hat 商标的产物，各种操作使用和付费版本没有区别，且完全免费。缺点是不向用户提供技术支持，也不负任何商业责任。有实力的公司可以选择付费版本，我们个人就使用CentOS。



### 3. 服务商的选购

服务商也是竞争激烈，因此经常开展促销活动，尤其是对于新用户有很大力度的优惠。阿里云是比较知名的，但是我以前在阿里云服务器上安装talib没有成功，talib是我们计算金融指标时必须用到的一个库，所以我这边给大家推荐使用Ucloud，这个是我一直在使用的，talib也安装过多次，都没问题的，其实价格也都是差不多的。

**全球大促服务器选购链接**：https://www.ucloud.cn/site/active/kuaijiesale.html?invitation_code=C1x3838320BA313#wulanchabu



### 4. 选择配置

服务器有不同的地区可以选择，我们做数字货币量化一定要选择海外服务器。也可以自定义配置，这就跟你买电脑一样，影音办公和游戏发烧的电脑配置价格肯定是不同的，配置越高价格就越贵，我们只用来部署策略的话是不需要多高配置的，看个人的经济承受能力和需求来决定选购什么配置的。

![image-20220519164954173](https://tva1.sinaimg.cn/large/e6c9d24ely1h2dt9dbytoj21jh0u0jx7.jpg)

![image-20220519165043920](https://tva1.sinaimg.cn/large/e6c9d24ely1h2dta663p8j21jl0u00x1.jpg)

镜像选择`CentOS 7.9`。Linux服务器的登录用户名默认都是root，密码就让它随机生成，大小写字母加数字混合，这样安全性较高一些，如果你是自己设置密码，也要尽量设置复杂一些，大小写字母、数字、特殊符号尽量都用上。然后记得把密码记录下来。



------

## 十七、服务器环境部署

### 1. SSH登陆

服务器放在服务商的机房，你不可能在机房操作你的 Linux 服务器。windows服务器可以通过远程桌面登陆，linux系统的服务器是通过 ssh 服务实现的远程登录功能，默认 ssh 服务端口号为 22。macbook可以使用终端登Linux 服务器，只需在终端中输入：

```
ssh root@101.36.117.260
```

这时候会要求你输入服务器的密码，输入密码按回车键即可。

windows电脑需要下载安装ssh登陆的客户端，我们这边下载一个[堡塔SSH终端](https://download.bt.cn/xterm/BT-Term.zip)并安装。

![image-20220227173955685](https://tva1.sinaimg.cn/large/e6c9d24ely1gzs7icr0f9j21bf0u0dkb.jpg)

安装完毕之后，新建远程会话连接：

![img](https://www.henenseo.com/wp-content/uploads/2020/12/ca208d111dea443459a9156ef393a570.jpg)

填写好相关信息点击`连接`即可登录到远程的linux服务器了。

### 2. 安装宝塔面板

>   宝塔Linux面板是提升运维效率的服务器管理软件，支持一键LAMP/LNMP/集群/监控/网站/FTP/数据库/JAVA等100多项服务器管理功能。
>   有30个人的专业团队研发及维护，经过200多个版本的迭代，功能全，少出错且足够安全，已获得全球百万用户认可安装。运维要高效，装宝塔。

使用 SSH 连接工具，如[堡塔SSH终端](https://download.bt.cn/xterm/BT-Term.zip)连接到您的 Linux 服务器后，根据系统执行相应命令开始安装（大约2分钟完成面板安装）：

Centos安装脚本 ：

```
yum install -y wget && wget -O install.sh http://download.bt.cn/install/install_6.0.sh && sh install.sh
```

安装完成后会出现面板登录信息：

```
==================================================================
外网面板地址: http://**************:8888/c81df8fa
内网面板地址: http://************:8888/c81df8fa
username: fioaxvaj
password: 46a657e0
If you cannot access the panel,
release the following panel port [8888] in the security group
若无法访问面板，请检查防火墙/安全组是否有放行面板[8888]端口
==================================================================
```

我们在自己的电脑上去访问这个地址，无法连接，我们去我们的服务器控制台修改一下外网防火墙，其中添加`8888`端口。然后再去访问这个链接就可以了，输入我们的宝塔面板的帐号和密码。

![image-20220227222713955](https://tva1.sinaimg.cn/large/e6c9d24ely1gzsftckhn6j21lp0u0jvf.jpg)

![image-20220227222747609](https://tva1.sinaimg.cn/large/e6c9d24ely1gzsftv7xx1j218w0o4dim.jpg)

前往外网防火墙页面，创建一个新的防火墙规则：

![image-20220227223304485](https://tva1.sinaimg.cn/large/e6c9d24ely1gzsfzduapsj21na0u042s.jpg)

+   22 ssh登录端口
+   8888 宝塔面板登录端口
+   3306 mysql数据库端口
+   888 phpmyadmin端口

![image-20220227223413275](https://tva1.sinaimg.cn/large/e6c9d24ely1gzsg0k2hfij21j30u042b.jpg)

回到之前的更换防火墙页面，点击那个右边的小圆圈刷新一下就可以看到新的stock防火墙规则了，然后选择它，点击确定即可。

然后我们在浏览器中打开那个`外网面板地址`，就可以打开登录页面了，输入账号和密码即可登录成功。它会要求我们绑定账号和密码，我们就通过以下链接注册一个宝塔账号：https://www.bt.cn/?invite_code=MV9wd2FteG4=

然后再绑定你注册的这个账号就可以了。

它会提示我们安装lnmp环境，你不装呢它就会一直提示，所以我们还是装一下，在这个过程中，它会帮我们把mysql数据库环境也装好。

>   **LNMP**是指一组通常一起使用来运行动态网站或者服务器的自由软件名称首字母缩写。L指Linux，N指Nginx，M一般指MySQL，也可以指MariaDB，P一般指PHP，也可以指Perl或Python。

我们打开终端，可以输入看一下`python`的版本，一般来说linux会默认安装python2.7。同时，安装宝塔面板的时候它会为我们安装一个新的python环境。系统自带的python环境可以用python和pip命令查看，宝塔面板安装的那个python环境可以用btypython和btpip命令查看。

### 3. 上传文件

我们在文件管理页面，到`www`目录下创建一个名为`PythonProjects`的目录，以后就把我们的python项目放到这个目录里，之所以选择这个目录，因为后续用进程管理器的时候，它默认打开的是这个目录，这样选择项目目录时会省事点。

![image-20220227224539118](https://tva1.sinaimg.cn/large/e6c9d24ely1gzsgcgfy2bj21dt0u0q72.jpg)

我们接着把我们的`HertelQuant`安装包上传到这个目录，解压后进入目录，在此目录下打开终端窗口，输入`btpython setup.py install`进行安装即可。

### 4. 安装talib

直接安装talib是安装不成功的，所以我们需要进行如下操作：

>   下载安装包：
>
>   ```
>   wget https://phoenixnap.dl.sourceforge.net/project/ta-lib/ta-lib/0.4.0/ta-lib-0.4.0-src.tar.gz
>   ```
>
>   解压：
>
>   ```
>   tar -xzf ta-lib-0.4.0-src.tar.gz
>   ```
>
>   安装c + +库：
>
>   ```
>   cd ta-lib
>   ./configure --prefix=/usr
>   make
>   make install
>   ```
>
>   查找 libta_lib.so.0文件路径：
>
>   ```
>   cd /usr
>   find -name libta_lib.so.0
>   ```
>
>   添加 /usr /lib 路径到环境变量中:
>
>   ```
>   echo "export LD_LIBRARY_PATH=/usr/lib" >> /etc/profile
>   ```
>
>   激活环境变量:
>
>   ```
>   source /etc/profile
>   ```
>
>   加载库:
>
>   ```
>   echo "/usr/lib/" >> /etc/ld.so.conf
>   ldconfig
>   ```

然后就可以`btpip install ta-lib`安装了。

这里为了简化这个流程，我写好了一个shell脚本:

`run.sh`：

```sh
#！/bin/sh

currentdir=`pwd`
echo $currentdir
echo "开始下载talib安装包..."
wget http://prdownloads.sourceforge.net/ta-lib/ta-lib-0.4.0-src.tar.gz
echo "talib安装包下载完毕，开始解压..."
tar -xzf ta-lib-0.4.0-src.tar.gz
echo "解压完成，开始安装c++库..."
cd ta-lib
./configure --prefix=/usr
make
make install
echo "c++库安装完成，开始查找 libta_lib.so.0文件路径..."
cd /usr
find -name libta_lib.so.0
echo "开始添加 /usr /lib 路径到环境变量中..."
echo "export LD_LIBRARY_PATH=/usr/lib" >> /etc/profile
echo "激活环境变量..."
source /etc/profile
echo "开始加载库..."
echo "/usr/lib/" >> /etc/ld.so.conf
ldconfig
cd $currentdir
echo "正在删除当前目录下的安装文件..."
rm -r ta-lib
rm ta-lib-0.4.0-src.tar.gz
echo "一切配置完成，开始安装talib到宝塔面板python解释器环境中..."
btpip install ta-lib
echo "talib安装完成..."
echo "开始进行测试，正在进入btpython交互式解释器环境..."
btpython ./test.py
```

`test.py`：

```python
"""
Author: Gary-Hertel
Email:  <EMAIL>
Date:   2022-03-04
"""

import requests
import numpy as np
import talib as tb


url = "https://push2his.eastmoney.com/api/qt/stock/kline/get?secid=1.600519&klt=101&fqt=1&lmt=1000&end=20500000&iscca=1&fields1=f1,f2,f3,f4,f5,f6,f7,f8,f9,f10,f11,f12,f13,f14&fields2=f51,f52,f53,f54,f55,f56,f57&iscr=0&iscca=0&ut=f057cbcbce2a86e2866ab8877db1d059&forcect=1"
response = requests.get(url).json()
ks = response["data"]["klines"]
close_list = [float(str(k).split(",")[2]) for k in ks]
close_array = np.array(close_list)
ma20 = tb.SMA(close_array, timeperiod=20)
latest_ma20 = ma20[-1]
print("最新一根K线上的ma20的值是:", latest_ma20)
print("talib已经测试完成，可以使用...")

```

这个`test.py`是用来测试talib是否可用的。

我们只需在`终端`中输入`./run.sh`即可运行`shell`脚本，即可自动编译安装`ta-lib`并且自动进行测试。

### 5. mongodb数据库

在软件商店中搜索mongodb，安装Mongodb，点击首页显示按钮。

进入mongodb安装目录，下面是宝塔面板的默认目录：

```
cd /www/server/mongodb/bin
```

输入命令行mongo，进入mongodb环境：

```
mongo
```

给admin设置用户密码：

```
db.createUser({user: 'root', pwd: '123456', roles: ['root']})
```

其中：

-   user: 用户名
-   pwd: 用户密码
-   roles: 用来设置用户的权限，比如读，读写 等等，可以设为root

验证是否添加成功，db.auth(用户名，用户密码)

```
db.auth('root', '123456')
```

如果返回 ‘1’表示验证成功， 如果是 ‘0’ 表示验证失败。

如果要删除用户 ：

```
db.dropUser(<user_name>) 删除某个用户，接受字符串参数
```

示例：

```
db.dropUser(“admin”)
```

宝塔面板配置开启权限验证以及允许外网访问:

开启外网访问 ： bindIp: 0.0.0.0

开启权限验证登录： authorization: enabled

记得如果要外网访问的话需要在服务器的控制台防火墙中放行27017端口，同时宝塔面板的`安全`那里也要放行`27017`端口。

### 6. python项目管理器的使用

在宝塔面板的`软件`一栏中搜索`python项目管理器`，安装`2.0 stable`版本，并且选择`首页显示`。

然后在`python项目管理器`那里安装一下`python3.7.9`:

![image-20220519171154675](https://tva1.sinaimg.cn/large/e6c9d24ely1h2dtw7vcmvj21jg0u0772.jpg)

接下来就可以创建项目了：

![image-20220519171313504](https://tva1.sinaimg.cn/large/e6c9d24ely1h2dtxllp60j21js0u0djq.jpg)



>    python项目管理器是为每个项目使用虚拟环境，因此我们的项目中必须包含`requirementx.txt`依赖文件，并且要将`Herte lQuant`安装包中的`quant`这个python包放到项目目录里，项目结构如下：
>
>   ```
>   .
>   ├── quant
>   ├── config.json
>   ├── main.py
>   ├── requirements.txt
>   ```



------

## 十八、回测功能

### 1. backtest.py

```python
"""
Author: Gary-Hertel
Email:  <EMAIL>
Date:   2022-03-23
"""


import sys
import threading

import pandas as pd

from typing import List
from dataclasses import dataclass


@dataclass
class Kline:
	date: List[str]
	open: List[float]
	high: List[float]
	low: List[float]
	close: List[float]
	volume: List[float]
	data: list


class Broker:
	"""回测底层。
	
	Args:
		csv_file_path: K线csv文件路径.
		on_event_kline_update_callback: K线更新回调函数.
		price_precision: 价格精度，默认为小数点后8位.
		quantity_precision: 数量精度，默认为小数点后8位.
	"""
	
	def __init__(self, csv_file_path: str, on_event_kline_update_callback=None, price_precision=8, quantity_precision=8):
		self._csv_file_path = csv_file_path
		self._kline = []
		self._already_finished_list = []
		self._on_event_kline_update_callback = on_event_kline_update_callback
		self._price_precision = price_precision
		self._quantity_precision = quantity_precision
		
		self._record = [
			["日期", "方向", "价格", "数量", "持多均价", "持多数量", "持空均价", "持空数量", "此次盈亏", "总资金"],
			["2100-01-01", "开始回测", 0, 0, 0, 0, 0, 0, 0, 100_0000]
		]
		
		threading.Thread(target=self._initialize_source_data).start()
		
	def _show_process_percent(self, percent):
		tags = ['—', '\\', '|', '/']
		out_string = "\r[当前回测进度] [%s] %s %3d%%" % (tags[(percent - 1) % 4], "█" * (percent // 2), percent)
		sys.stdout.write(out_string)
		sys.stdout.flush()
	
	def _initialize_source_data(self):
		df = pd.read_csv(self._csv_file_path)
		data = df.values.tolist()
		length = len(data)
		for d in data:
			self._kline.append(d)
			self._already_finished_list.append(d)
			percent = int(len(self._already_finished_list) / length * 100)
			self._show_process_percent(percent)
			if len(self._kline) >= 500:
				del self._kline[-250: 0: -1]
			kline = Kline(
				date=[k[0] for k in self._kline],
				open=[k[1] for k in self._kline],
				high=[k[2] for k in self._kline],
				low=[k[3] for k in self._kline],
				close=[k[4] for k in self._kline],
				volume=[k[5] for k in self._kline],
				data=self._kline
			)
			self._on_event_kline_update_callback(kline)
		
		self._record.pop(0)
		self._record.pop(0)
		record_df = pd.DataFrame(self._record, columns=["日期", "方向", "价格", "数量", "持多均价", "持多数量", "持空均价", "持空数量", "此次盈亏", "总资金"])
		record_df = record_df.drop("持多均价", axis=1)
		record_df = record_df.drop("持多数量", axis=1)
		record_df = record_df.drop("持空均价", axis=1)
		record_df = record_df.drop("持空数量", axis=1)
		record_df.to_csv("record.csv", index=False)
	
	@property
	def long_quantity(self):
		long_quantity = float(self._record[-1][5])
		return long_quantity
	
	@property
	def long_avg_price(self):
		long_avg_price = float(self._record[-1][4])
		return long_avg_price
	
	@property
	def short_quantity(self):
		short_quantity = float(self._record[-1][7])
		return short_quantity
	
	@property
	def short_avg_price(self):
		short_avg_price = float(self._record[-1][6])
		return short_avg_price
	
	@property
	def asset(self):
		asset = float(self._record[-1][9])
		return asset
	
	def buy(self, price, quantity, action="买入开多"):
		date = self._kline[-1][0]
		hold_quantity = float(self._record[-1][5]) + quantity
		hold_avg_price = (float(self._record[-1][4]) * float(self._record[-1][5]) + price * quantity) / hold_quantity
		profit = ""
		asset = self._record[-1][9]
		self._record.append([date, action, round(price, self._price_precision), round(quantity, self._quantity_precision), hold_avg_price, hold_quantity, 0, 0, profit, asset])
	
	def sell(self, price, quantity, action="卖出平多"):
		date = self._kline[-1][0]
		hold_quantity = float(self._record[-1][5]) - quantity
		hold_avg_price = (float(self._record[-1][5]) * float(self._record[-1][4]) - price * quantity) / hold_quantity if hold_quantity > 0 else 0
		profit = int((price - float(self._record[-1][4])) * quantity)
		asset = int(self._record[-1][9]) + profit
		self._record.append([date, action, round(price, self._price_precision), round(quantity, self._quantity_precision), hold_avg_price, hold_quantity, 0, 0, profit, asset])
		
	def sell_short(self, price, quantity, action="卖出开空"):
		date = self._kline[-1][0]
		hold_quantity = float(self._record[-1][7]) + quantity
		hold_avg_price = (float(self._record[-1][6]) * float(self._record[-1][7]) + price * quantity) / hold_quantity
		profit = ""
		asset = self._record[-1][9]
		self._record.append([date, action, round(price, self._price_precision), round(quantity, self._quantity_precision), 0, 0, hold_avg_price, hold_quantity, profit, asset])
		
	def buy_to_cover(self, price, quantity, action="买入平空"):
		date = self._kline[-1][0]
		hold_quantity = float(self._record[-1][7]) - quantity
		hold_avg_price = (float(self._record[-1][7]) * float(self._record[-1][6]) - price * quantity) / hold_quantity if hold_quantity > 0 else 0
		profit = int((float(self._record[-1][6]) - price) * quantity)
		asset = int(self._record[-1][9]) + profit
		self._record.append([date, action, round(price, self._price_precision), round(quantity, self._quantity_precision), 0, 0, hold_avg_price, hold_quantity, profit, asset])
		
```

### 2. strategy.py

```python
"""双均线多空策略回测"""


import talib
import numpy

from backtest import Broker
from backtest import Kline


class Strategy:
	
	def __init__(self):
		self.broker = Broker(
			csv_file_path="BTCUSDT_2021_1m.csv",
			on_event_kline_update_callback=self.on_event_kline_update_callback,
			price_precision=8,
			quantity_precision=8
		)
	
	def on_event_kline_update_callback(self, kline: Kline):
		
		if len(kline.data) < 20:
			return
		
		close_array = numpy.array(kline.close)
		
		# 计算技术指标
		fast_ma = talib.SMA(close_array, 20)  # 计算短周期均线
		slow_ma = talib.SMA(close_array, 30)  # 计算长周期均线
		
		# 不用当根k线上的ma来计算信号，防止信号闪烁
		cross_over = fast_ma[-2] >= slow_ma[-2] and fast_ma[-3] < slow_ma[-3]  # 金叉
		cross_below = slow_ma[-2] >= fast_ma[-2] and slow_ma[-3] < fast_ma[-3]  # 死叉
		
		# 金叉时
		if cross_over:
			# 如果持空头就先平空再开多
			if self.broker.short_quantity != 0:
				self.broker.buy_to_cover(price=kline.close[-1], quantity=self.broker.short_quantity)
				self.broker.buy(price=kline.close[-1], quantity=self.broker.asset / kline.close[-1])
			# 如果未持空头且未持多头就直接买入开多
			elif self.broker.short_quantity == 0 and self.broker.short_quantity == 0:
				self.broker.buy(price=kline.close[-1], quantity=self.broker.asset / kline.close[-1])
		
		# 死叉时
		elif cross_below:
			# 如果持多就先平多再开空
			if self.broker.long_quantity > 0:
				self.broker.sell(price=kline.close[-1], quantity=self.broker.long_quantity)
				self.broker.sell_short(price=kline.close[-1], quantity=self.broker.asset / kline.close[-1])
			# 如果未持多且未持空就直接卖出开空
			elif self.broker.long_quantity == 0 and self.broker.short_quantity == 0:
				self.broker.sell_short(price=kline.close[-1], quantity=self.broker.asset / kline.close[-1])
		
		# 当根K线最低价小于等于开仓价格的99%，即亏损1%时，且当前有持多，全仓卖出止损
		elif kline.low[-1] <= self.broker.long_avg_price * 0.99 and self.broker.long_quantity > 0:
			self.broker.sell(price=self.broker.long_avg_price * 0.99, quantity=self.broker.long_quantity, action="止损平多")
		
		# 持空时，当根k线最高价大于等于开仓价格的101%，即亏损1%时，且当前持空，全仓买入止损
		elif kline.high[-1] >= self.broker.short_avg_price * 1.01 and self.broker.short_quantity > 0:
			self.broker.buy_to_cover(price=self.broker.short_avg_price * 1.01, quantity=self.broker.short_quantity, action="止损平空")


if __name__ == '__main__':
	Strategy()
```

### 3. K线数据样本局部

|          **t**          |     **o**      |     **h**      |     **l**      |     **c**      |    **v**    |
| :---------------------: | :------------: | :------------: | :------------: | :------------: | :---------: |
| **2021-01-01 00:00:00** | 28782.01000000 | 28821.85000000 | 28763.94000000 | 28811.85000000 | 95.83579500 |
| **2021-01-01 00:01:00** | 28812.64000000 | 28822.59000000 | 28714.29000000 | 28726.62000000 | 58.51622700 |
| **2021-01-01 00:02:00** | 28728.28000000 | 28744.76000000 | 28684.69000000 | 28693.37000000 | 75.03837300 |
| **2021-01-01 00:03:00** | 28693.37000000 | 28715.15000000 | 28682.09000000 | 28690.29000000 | 37.12819300 |
| **2021-01-01 00:04:00** | 28690.29000000 | 28734.70000000 | 28680.00000000 | 28715.11000000 | 38.41111200 |
| **2021-01-01 00:05:00** | 28715.11000000 | 28741.26000000 | 28713.06000000 | 28735.88000000 | 25.01118200 |
| **2021-01-01 00:06:00** | 28735.89000000 | 28747.43000000 | 28720.87000000 | 28732.01000000 | 28.18258400 |
| **2021-01-01 00:07:00** | 28729.43000000 | 28729.43000000 | 28700.00000000 | 28700.01000000 | 24.41429000 |
| **2021-01-01 00:08:00** | 28700.00000000 | 28720.00000000 | 28682.08000000 | 28720.00000000 | 18.95493900 |

### 4. 回测记录样本局部

|        **日期**         | **方向** | **价格** |  **数量**   | **此次盈亏** | **总资金** |
| :---------------------: | :------: | :------: | :---------: | :----------: | :--------: |
| **2021-01-01 00:38:00** | 买入开多 | 28620.01 | 34.94058877 |              |  1000000   |
| **2021-01-01 00:44:00** | 卖出平多 | 28490.3  | 34.94058877 |    -4532     |   995468   |
| **2021-01-01 00:44:00** | 卖出开空 | 28490.3  | 34.94059382 |              |   995468   |
| **2021-01-01 01:14:00** | 买入平空 | 28576.02 | 34.94059382 |    -2995     |   992473   |
| **2021-01-01 01:14:00** | 买入开多 | 28576.02 | 34.73097373 |              |   992473   |
| **2021-01-01 01:52:00** | 卖出平多 | 28634.62 | 34.73097373 |     2035     |   994508   |
| **2021-01-01 01:52:00** | 卖出开空 | 28634.62 | 34.73096552 |              |   994508   |
| **2021-01-01 01:58:00** | 买入平空 | 28566.12 | 34.73096552 |     2379     |   996887   |
| **2021-01-01 01:58:00** | 买入开多 | 28566.12 | 34.89752896 |              |   996887   |



------

## 十九、版本更新记录

```
日期：2022-05-20
版本：1.0.2
作者：Gary-Hertel
~~~~~~~~~~~~~~~~~~
1.Binance Swap新增的bookTicker订阅重复订阅问题修复。
```

```
日期：2022-06-13
版本：1.0.3
作者：Gary-Hertel
~~~~~~~~~~~~~~~~~~
1.修复常量改为小写后K线部分的一些大小写错误。
```

```commandline
日期：2022-06-23
版本：1.0.4
作者：Gary-Hertel
~~~~~~~~~~~~~~~~~~
1.修复OKX持仓推送时只取第一个值从而无法接收到全部持仓的问题
```