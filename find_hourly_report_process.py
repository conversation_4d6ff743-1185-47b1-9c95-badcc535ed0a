#!/usr/bin/env python3
"""
查找并关闭发送小时报告的进程
定位发送"📊 小时报告 📊"消息的进程
"""

import psutil
import os
import signal
import subprocess
import time
from datetime import datetime
import logging
import re

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class HourlyReportProcessFinder:
    """小时报告进程查找器"""
    
    def __init__(self):
        self.report_keywords = [
            "小时报告",
            "hourly",
            "report",
            "统计",
            "钉钉",
            "dingding",
            "notification",
            "notify"
        ]
        self.current_dir = os.getcwd()
    
    def find_processes_by_keywords(self):
        """根据关键词查找进程"""
        matching_processes = []
        
        for proc in psutil.process_iter(['pid', 'name', 'cmdline', 'create_time', 'cwd']):
            try:
                if not proc.info['cmdline']:
                    continue
                
                cmdline_str = ' '.join(proc.info['cmdline']).lower()
                
                # 检查是否包含相关关键词
                for keyword in self.report_keywords:
                    if keyword in cmdline_str:
                        matching_processes.append({
                            'pid': proc.info['pid'],
                            'name': proc.info['name'],
                            'cmdline': proc.info['cmdline'],
                            'create_time': datetime.fromtimestamp(proc.info['create_time']),
                            'cwd': proc.info['cwd'],
                            'keyword_matched': keyword
                        })
                        break
                
            except (psutil.NoSuchProcess, psutil.AccessDenied):
                continue
        
        return matching_processes
    
    def search_code_files(self):
        """搜索代码文件中的小时报告相关代码"""
        logger.info("🔍 搜索代码文件中的小时报告相关内容...")
        
        report_files = []
        search_patterns = [
            r"小时报告",
            r"hourly.*report",
            r"21:54",
            r"今日交易.*0.*笔",
            r"今日盈亏.*0\.00",
            r"信号发送统计",
            r"系统运行正常.*继续监控中"
        ]
        
        # 搜索Python文件
        for root, dirs, files in os.walk('.'):
            # 跳过一些不必要的目录
            dirs[:] = [d for d in dirs if not d.startswith('.') and d not in ['__pycache__', 'node_modules']]
            
            for file in files:
                if file.endswith(('.py', '.js', '.ts')):
                    file_path = os.path.join(root, file)
                    try:
                        with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                            content = f.read()
                            
                            matches = []
                            for pattern in search_patterns:
                                if re.search(pattern, content, re.IGNORECASE):
                                    matches.append(pattern)
                            
                            if matches:
                                report_files.append({
                                    'file': file_path,
                                    'matches': matches,
                                    'size': os.path.getsize(file_path)
                                })
                    except Exception as e:
                        continue
        
        return report_files
    
    def check_cron_jobs(self):
        """检查定时任务"""
        logger.info("⏰ 检查定时任务...")
        
        cron_info = []
        
        try:
            # 检查用户的crontab
            result = subprocess.run(['crontab', '-l'], capture_output=True, text=True)
            if result.returncode == 0 and result.stdout.strip():
                cron_info.append({
                    'type': 'user_crontab',
                    'content': result.stdout
                })
        except:
            pass
        
        # 检查系统cron目录
        cron_dirs = ['/etc/cron.d', '/etc/cron.hourly', '/etc/cron.daily']
        for cron_dir in cron_dirs:
            if os.path.exists(cron_dir):
                try:
                    files = os.listdir(cron_dir)
                    if files:
                        cron_info.append({
                            'type': f'system_cron_{os.path.basename(cron_dir)}',
                            'files': files
                        })
                except:
                    pass
        
        return cron_info
    
    def analyze_settlement_checker(self):
        """分析settlement_checker.py文件"""
        logger.info("🔍 分析settlement_checker.py文件...")
        
        settlement_file = "quant/settlement_checker.py"
        if not os.path.exists(settlement_file):
            return None
        
        try:
            with open(settlement_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 查找相关的方法和类
            analysis = {
                'file_size': os.path.getsize(settlement_file),
                'has_hourly_report': False,
                'has_notification': False,
                'has_dingding': False,
                'suspicious_methods': []
            }
            
            # 检查是否包含小时报告相关内容
            if re.search(r"小时报告|hourly.*report", content, re.IGNORECASE):
                analysis['has_hourly_report'] = True
            
            if re.search(r"通知|notification|notify", content, re.IGNORECASE):
                analysis['has_notification'] = True
            
            if re.search(r"钉钉|dingding", content, re.IGNORECASE):
                analysis['has_dingding'] = True
            
            # 查找可疑的方法
            method_patterns = [
                r"def.*report.*\(",
                r"def.*notify.*\(",
                r"def.*send.*\(",
                r"def.*hourly.*\("
            ]
            
            for pattern in method_patterns:
                matches = re.findall(pattern, content, re.IGNORECASE)
                analysis['suspicious_methods'].extend(matches)
            
            return analysis
            
        except Exception as e:
            logger.error(f"分析settlement_checker.py失败: {e}")
            return None
    
    def find_running_python_processes(self):
        """查找所有运行中的Python进程"""
        python_processes = []
        
        for proc in psutil.process_iter(['pid', 'name', 'cmdline', 'create_time', 'cwd']):
            try:
                if proc.info['name'] in ['Python', 'python', 'python3']:
                    if proc.info['cmdline']:
                        python_processes.append({
                            'pid': proc.info['pid'],
                            'cmdline': ' '.join(proc.info['cmdline']),
                            'create_time': datetime.fromtimestamp(proc.info['create_time']),
                            'cwd': proc.info['cwd']
                        })
            except (psutil.NoSuchProcess, psutil.AccessDenied):
                continue
        
        return python_processes
    
    def display_findings(self, processes, code_files, cron_jobs, settlement_analysis, python_processes):
        """显示查找结果"""
        print("\n" + "="*80)
        print("🔍 小时报告进程查找结果")
        print("="*80)
        
        # 1. 关键词匹配的进程
        if processes:
            print(f"\n📋 发现 {len(processes)} 个关键词匹配的进程:")
            for proc in processes:
                print(f"  PID {proc['pid']}: {proc['name']}")
                print(f"    命令行: {' '.join(proc['cmdline'])}")
                print(f"    匹配关键词: {proc['keyword_matched']}")
                print(f"    启动时间: {proc['create_time']}")
                print(f"    工作目录: {proc['cwd']}")
                print()
        else:
            print("\n✅ 未发现关键词匹配的进程")
        
        # 2. 代码文件分析
        if code_files:
            print(f"\n📄 发现 {len(code_files)} 个包含相关内容的代码文件:")
            for file_info in code_files:
                print(f"  📁 {file_info['file']} ({file_info['size']} bytes)")
                print(f"    匹配模式: {', '.join(file_info['matches'])}")
        else:
            print("\n✅ 未在代码文件中发现相关内容")
        
        # 3. settlement_checker.py分析
        if settlement_analysis:
            print(f"\n🔍 settlement_checker.py 分析:")
            print(f"  文件大小: {settlement_analysis['file_size']} bytes")
            print(f"  包含小时报告: {'是' if settlement_analysis['has_hourly_report'] else '否'}")
            print(f"  包含通知功能: {'是' if settlement_analysis['has_notification'] else '否'}")
            print(f"  包含钉钉功能: {'是' if settlement_analysis['has_dingding'] else '否'}")
            if settlement_analysis['suspicious_methods']:
                print(f"  可疑方法: {', '.join(settlement_analysis['suspicious_methods'])}")
        
        # 4. 定时任务
        if cron_jobs:
            print(f"\n⏰ 发现 {len(cron_jobs)} 个定时任务:")
            for cron in cron_jobs:
                print(f"  类型: {cron['type']}")
                if 'content' in cron:
                    print(f"  内容: {cron['content'][:200]}...")
                elif 'files' in cron:
                    print(f"  文件: {', '.join(cron['files'])}")
        else:
            print("\n✅ 未发现相关定时任务")
        
        # 5. Python进程
        print(f"\n🐍 当前运行的Python进程 ({len(python_processes)} 个):")
        for proc in python_processes:
            print(f"  PID {proc['pid']}: {proc['cmdline'][:80]}...")
            print(f"    启动时间: {proc['create_time']}")
    
    def kill_process_interactive(self, processes):
        """交互式关闭进程"""
        if not processes:
            print("\n✅ 没有需要关闭的进程")
            return
        
        print(f"\n⚠️ 发现 {len(processes)} 个可疑进程")
        
        for i, proc in enumerate(processes, 1):
            print(f"\n进程 {i}:")
            print(f"  PID: {proc['pid']}")
            print(f"  命令: {' '.join(proc['cmdline'])}")
            print(f"  匹配关键词: {proc['keyword_matched']}")
            
            response = input(f"是否关闭此进程? (y/N): ").strip().lower()
            if response == 'y':
                try:
                    p = psutil.Process(proc['pid'])
                    p.terminate()
                    time.sleep(2)
                    
                    if p.is_running():
                        p.kill()
                        print(f"✅ 强制关闭进程 {proc['pid']}")
                    else:
                        print(f"✅ 优雅关闭进程 {proc['pid']}")
                        
                except psutil.NoSuchProcess:
                    print(f"ℹ️ 进程 {proc['pid']} 已不存在")
                except psutil.AccessDenied:
                    print(f"❌ 无权限关闭进程 {proc['pid']}")
                except Exception as e:
                    print(f"❌ 关闭进程 {proc['pid']} 失败: {e}")

def main():
    """主函数"""
    print("🔍 查找小时报告进程")
    print("="*50)
    
    finder = HourlyReportProcessFinder()
    
    # 1. 查找关键词匹配的进程
    logger.info("查找关键词匹配的进程...")
    processes = finder.find_processes_by_keywords()
    
    # 2. 搜索代码文件
    code_files = finder.search_code_files()
    
    # 3. 检查定时任务
    cron_jobs = finder.check_cron_jobs()
    
    # 4. 分析settlement_checker.py
    settlement_analysis = finder.analyze_settlement_checker()
    
    # 5. 查找Python进程
    python_processes = finder.find_running_python_processes()
    
    # 6. 显示结果
    finder.display_findings(processes, code_files, cron_jobs, settlement_analysis, python_processes)
    
    # 7. 交互式关闭进程
    if processes:
        finder.kill_process_interactive(processes)
    
    print("\n✅ 查找完成!")
    
    # 建议
    print("\n💡 建议:")
    print("1. 检查 settlement_checker.py 中的通知相关代码")
    print("2. 查看主进程 main.py 是否包含定时报告功能")
    print("3. 检查是否有独立的报告发送脚本在运行")
    print("4. 查看系统日志确认消息来源")

if __name__ == "__main__":
    main()
