#!/usr/bin/env python3
"""
修复数据库中已结算信号的准确性问题
重新计算盈亏并修正错误的结算结果
"""

import sqlite3
import os
from datetime import datetime, timedelta
import logging

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def fix_settlement_issues():
    """修复已结算信号的准确性问题"""
    try:
        logger.info("🔧 开始修复数据库中已结算信号的准确性问题...")
        
        # 连接数据库
        db_path = "trading_system.db"
        if not os.path.exists(db_path):
            logger.error("❌ 未找到数据库文件")
            return False
        
        conn = sqlite3.connect(db_path)
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()
        
        # 1. 修复盈亏计算错误
        logger.info("\n🔧 步骤1: 修复盈亏计算错误")
        
        # 获取所有已结算的交易
        cursor.execute("""
            SELECT * FROM trade_history 
            WHERE status IN ('WIN', 'LOSS') 
            AND exit_price IS NOT NULL 
            AND entry_price IS NOT NULL
        """)
        
        settled_trades = cursor.fetchall()
        logger.info(f"找到 {len(settled_trades)} 个已结算的交易需要检查")
        
        fixed_count = 0
        for trade in settled_trades:
            # 重新计算正确的盈亏
            entry_price = trade['entry_price']
            exit_price = trade['exit_price']
            direction = trade['direction']
            suggested_bet = trade['suggested_bet']
            
            if direction == "LONG":
                # 做多：(出场价 - 入场价) / 入场价 * 投注金额
                price_change_pct = (exit_price - entry_price) / entry_price
                correct_pnl = price_change_pct * suggested_bet
            else:  # SHORT
                # 做空：(入场价 - 出场价) / 入场价 * 投注金额
                price_change_pct = (entry_price - exit_price) / entry_price
                correct_pnl = price_change_pct * suggested_bet
            
            # 确定正确的状态
            correct_status = "WIN" if correct_pnl > 0 else "LOSS"
            
            # 检查是否需要修复
            current_pnl = trade['pnl']
            current_status = trade['status']
            
            if (abs(current_pnl - correct_pnl) > 0.01 or current_status != correct_status):
                # 更新数据库
                cursor.execute("""
                    UPDATE trade_history 
                    SET pnl = ?, status = ?
                    WHERE id = ?
                """, (correct_pnl, correct_status, trade['id']))
                
                logger.info(f"  修复交易ID {trade['id']}: 盈亏 {current_pnl:.2f} -> {correct_pnl:.2f}, 状态 {current_status} -> {correct_status}")
                fixed_count += 1
        
        logger.info(f"✅ 修复了 {fixed_count} 个交易的盈亏计算")
        
        # 2. 处理超时未结算的交易
        logger.info("\n🔧 步骤2: 处理超时未结算的交易")
        
        cutoff_time = (datetime.utcnow() - timedelta(minutes=15)).isoformat()
        cursor.execute("""
            SELECT * FROM trade_history 
            WHERE status = 'PENDING' 
            AND signal_timestamp < ?
        """, (cutoff_time,))
        
        overdue_trades = cursor.fetchall()
        logger.info(f"找到 {len(overdue_trades)} 个超时未结算的交易")
        
        # 这里我们将这些交易标记为需要手动处理，而不是强制结算
        # 因为没有实时价格数据来进行结算
        for trade in overdue_trades:
            logger.warning(f"  超时交易ID {trade['id']}: 信号时间 {trade['signal_timestamp']}")
        
        # 3. 验证修复结果
        logger.info("\n🔧 步骤3: 验证修复结果")
        
        cursor.execute("""
            SELECT COUNT(*) as total,
                   SUM(CASE WHEN status = 'WIN' THEN 1 ELSE 0 END) as wins,
                   SUM(CASE WHEN status = 'LOSS' THEN 1 ELSE 0 END) as losses,
                   AVG(pnl) as avg_pnl,
                   SUM(pnl) as total_pnl
            FROM trade_history 
            WHERE status IN ('WIN', 'LOSS')
        """)
        
        stats = cursor.fetchone()
        win_rate = (stats['wins'] / stats['total'] * 100) if stats['total'] > 0 else 0
        
        logger.info(f"修复后统计:")
        logger.info(f"  总交易数: {stats['total']}")
        logger.info(f"  胜率: {win_rate:.1f}% ({stats['wins']}胜 {stats['losses']}负)")
        logger.info(f"  平均盈亏: {stats['avg_pnl']:.2f}")
        logger.info(f"  总盈亏: {stats['total_pnl']:.2f}")
        
        # 提交更改
        conn.commit()
        conn.close()
        
        logger.info("\n✅ 修复完成!")
        return True
        
    except Exception as e:
        logger.error(f"修复过程中发生错误: {e}")
        import traceback
        logger.error(f"详细错误信息: {traceback.format_exc()}")
        return False

def create_settlement_fix_strategy():
    """创建结算修复策略类"""
    logger.info("\n🔧 创建结算修复策略...")
    
    strategy_content = '''"""
结算修复策略 - 修正已结算信号的计算错误
"""

import sqlite3
from datetime import datetime, timedelta
from typing import Dict, Any, List
import logging

logger = logging.getLogger(__name__)

class SettlementFixStrategy:
    """结算修复策略类"""
    
    def __init__(self, db_path: str = "trading_system.db"):
        self.db_path = db_path
    
    def recalculate_pnl(self, entry_price: float, exit_price: float, 
                       direction: str, suggested_bet: float) -> tuple[float, str]:
        """重新计算正确的盈亏和状态"""
        if direction == "LONG":
            # 做多：(出场价 - 入场价) / 入场价 * 投注金额
            price_change_pct = (exit_price - entry_price) / entry_price
            pnl = price_change_pct * suggested_bet
        else:  # SHORT
            # 做空：(入场价 - 出场价) / 入场价 * 投注金额
            price_change_pct = (entry_price - exit_price) / entry_price
            pnl = price_change_pct * suggested_bet
        
        status = "WIN" if pnl > 0 else "LOSS"
        return pnl, status
    
    def fix_all_settlements(self) -> Dict[str, Any]:
        """修复所有结算错误"""
        try:
            conn = sqlite3.connect(self.db_path)
            conn.row_factory = sqlite3.Row
            cursor = conn.cursor()
            
            # 获取所有已结算交易
            cursor.execute("""
                SELECT * FROM trade_history 
                WHERE status IN ('WIN', 'LOSS') 
                AND exit_price IS NOT NULL 
                AND entry_price IS NOT NULL
            """)
            
            trades = cursor.fetchall()
            fixed_count = 0
            
            for trade in trades:
                correct_pnl, correct_status = self.recalculate_pnl(
                    trade['entry_price'], 
                    trade['exit_price'],
                    trade['direction'], 
                    trade['suggested_bet']
                )
                
                # 检查是否需要修复
                if (abs(trade['pnl'] - correct_pnl) > 0.01 or 
                    trade['status'] != correct_status):
                    
                    cursor.execute("""
                        UPDATE trade_history 
                        SET pnl = ?, status = ?
                        WHERE id = ?
                    """, (correct_pnl, correct_status, trade['id']))
                    
                    fixed_count += 1
            
            conn.commit()
            conn.close()
            
            return {
                'success': True,
                'fixed_count': fixed_count,
                'total_trades': len(trades)
            }
            
        except Exception as e:
            logger.error(f"修复结算错误失败: {e}")
            return {'success': False, 'error': str(e)}
'''
    
    # 保存策略文件
    with open('quant/strategies/settlement_fix_strategy.py', 'w', encoding='utf-8') as f:
        f.write(strategy_content)
    
    logger.info("✅ 结算修复策略已创建: quant/strategies/settlement_fix_strategy.py")

if __name__ == "__main__":
    # 执行修复
    success = fix_settlement_issues()
    
    if success:
        # 创建修复策略
        create_settlement_fix_strategy()
        logger.info("\n🎉 所有修复工作完成!")
    else:
        logger.error("❌ 修复失败!")
