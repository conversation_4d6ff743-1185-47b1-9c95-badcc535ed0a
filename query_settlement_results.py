#!/usr/bin/env python3
"""
查询并输出所有已结算交易信号的详细结算结果
"""

import sqlite3
import os
from datetime import datetime
import logging

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def query_all_settlement_results():
    """查询所有已结算交易的详细结果"""
    try:
        logger.info("📊 开始查询所有已结算交易信号的详细结算结果...")
        
        # 连接数据库
        db_path = "trading_system.db"
        if not os.path.exists(db_path):
            logger.error("❌ 未找到数据库文件")
            return None
        
        conn = sqlite3.connect(db_path)
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()
        
        # 查询所有已结算的交易
        query = """
        SELECT 
            id,
            signal_timestamp,
            direction,
            entry_price,
            exit_price,
            status,
            pnl,
            exit_timestamp,
            suggested_bet,
            confidence_score,
            symbol,
            (julianday(exit_timestamp) - julianday(signal_timestamp)) * 24 * 60 as settlement_time_diff
        FROM trade_history 
        WHERE status IN ('WIN', 'LOSS', 'TIMEOUT')
        AND exit_timestamp IS NOT NULL
        ORDER BY signal_timestamp ASC
        """
        
        cursor.execute(query)
        trades = cursor.fetchall()
        
        logger.info(f"找到 {len(trades)} 个已结算的交易记录")
        
        # 输出表头
        print("\n" + "="*150)
        print("📊 所有已结算交易信号详细结算结果")
        print("="*150)
        
        # 表格标题
        header = f"{'ID':<4} {'信号时间':<20} {'方向':<5} {'入场价':<12} {'出场价':<12} {'状态':<8} {'盈亏':<10} {'结算时间':<20} {'时间差':<8} {'时机':<8} {'投注':<6} {'置信度':<8}"
        print(header)
        print("-" * 150)
        
        # 统计数据
        total_trades = len(trades)
        win_count = 0
        loss_count = 0
        timeout_count = 0
        total_pnl = 0
        timing_accurate = 0
        timing_early = 0
        timing_late = 0
        timing_very_late = 0
        
        # 输出每个交易的详细信息
        for trade in trades:
            # 格式化时间
            signal_time = datetime.fromisoformat(trade['signal_timestamp']).strftime('%Y-%m-%d %H:%M:%S')
            exit_time = datetime.fromisoformat(trade['exit_timestamp']).strftime('%Y-%m-%d %H:%M:%S')
            
            # 计算时间差和时机判断
            time_diff = trade['settlement_time_diff']
            if time_diff < 8:
                timing_status = "提前"
                timing_early += 1
            elif 8 <= time_diff <= 12:
                timing_status = "准确"
                timing_accurate += 1
            elif 12 < time_diff <= 15:
                timing_status = "延迟"
                timing_late += 1
            else:
                timing_status = "严重延迟"
                timing_very_late += 1
            
            # 统计状态
            if trade['status'] == 'WIN':
                win_count += 1
            elif trade['status'] == 'LOSS':
                loss_count += 1
            elif trade['status'] == 'TIMEOUT':
                timeout_count += 1
            
            # 累计盈亏
            if trade['pnl'] is not None:
                total_pnl += trade['pnl']
            
            # 格式化输出，处理None值
            exit_price = trade['exit_price'] if trade['exit_price'] is not None else 0.0
            pnl = trade['pnl'] if trade['pnl'] is not None else 0.0
            suggested_bet = trade['suggested_bet'] if trade['suggested_bet'] is not None else 0.0
            confidence_score = trade['confidence_score'] if trade['confidence_score'] is not None else 0.0

            row = f"{trade['id']:<4} {signal_time:<20} {trade['direction']:<5} {trade['entry_price']:<12.2f} {exit_price:<12.2f} {trade['status']:<8} {pnl:<10.2f} {exit_time:<20} {time_diff:<8.1f} {timing_status:<8} {suggested_bet:<6.1f} {confidence_score:<8.2f}"
            print(row)
        
        # 输出统计摘要
        print("\n" + "="*150)
        print("📈 结算结果统计摘要")
        print("="*150)
        
        win_rate = (win_count / (win_count + loss_count) * 100) if (win_count + loss_count) > 0 else 0
        timing_accuracy_rate = (timing_accurate / total_trades * 100) if total_trades > 0 else 0
        
        print(f"总交易数: {total_trades}")
        print(f"胜利交易: {win_count} ({win_count/total_trades*100:.1f}%)")
        print(f"失败交易: {loss_count} ({loss_count/total_trades*100:.1f}%)")
        print(f"超时交易: {timeout_count} ({timeout_count/total_trades*100:.1f}%)")
        print(f"胜率: {win_rate:.1f}% (不含超时)")
        print(f"总盈亏: {total_pnl:.2f}")
        print(f"平均盈亏: {total_pnl/total_trades:.2f}")
        
        print(f"\n⏰ 结算时机分析:")
        print(f"准确结算 (8-12分钟): {timing_accurate} ({timing_accurate/total_trades*100:.1f}%)")
        print(f"提前结算 (<8分钟): {timing_early} ({timing_early/total_trades*100:.1f}%)")
        print(f"延迟结算 (12-15分钟): {timing_late} ({timing_late/total_trades*100:.1f}%)")
        print(f"严重延迟 (>15分钟): {timing_very_late} ({timing_very_late/total_trades*100:.1f}%)")
        print(f"时机准确率: {timing_accuracy_rate:.1f}%")
        
        # 按状态分组的详细分析
        print(f"\n💰 盈亏分析:")
        cursor.execute("""
            SELECT 
                status,
                COUNT(*) as count,
                AVG(pnl) as avg_pnl,
                SUM(pnl) as total_pnl,
                MIN(pnl) as min_pnl,
                MAX(pnl) as max_pnl
            FROM trade_history 
            WHERE status IN ('WIN', 'LOSS', 'TIMEOUT')
            GROUP BY status
        """)
        
        status_stats = cursor.fetchall()
        for stat in status_stats:
            print(f"{stat['status']}: {stat['count']}笔, 平均{stat['avg_pnl']:.2f}, 总计{stat['total_pnl']:.2f}, 范围[{stat['min_pnl']:.2f}, {stat['max_pnl']:.2f}]")
        
        # 按方向分析
        print(f"\n📊 交易方向分析:")
        cursor.execute("""
            SELECT 
                direction,
                COUNT(*) as count,
                SUM(CASE WHEN status = 'WIN' THEN 1 ELSE 0 END) as wins,
                AVG(pnl) as avg_pnl,
                SUM(pnl) as total_pnl
            FROM trade_history 
            WHERE status IN ('WIN', 'LOSS')
            GROUP BY direction
        """)
        
        direction_stats = cursor.fetchall()
        for stat in direction_stats:
            direction_win_rate = (stat['wins'] / stat['count'] * 100) if stat['count'] > 0 else 0
            print(f"{stat['direction']}: {stat['count']}笔, 胜率{direction_win_rate:.1f}%, 平均盈亏{stat['avg_pnl']:.2f}, 总盈亏{stat['total_pnl']:.2f}")
        
        conn.close()
        
        return {
            'total_trades': total_trades,
            'win_count': win_count,
            'loss_count': loss_count,
            'timeout_count': timeout_count,
            'win_rate': win_rate,
            'total_pnl': total_pnl,
            'timing_accuracy_rate': timing_accuracy_rate
        }
        
    except Exception as e:
        logger.error(f"查询过程中发生错误: {e}")
        import traceback
        logger.error(f"详细错误信息: {traceback.format_exc()}")
        return None

def export_to_csv():
    """导出结算结果到CSV文件"""
    try:
        logger.info("📄 导出结算结果到CSV文件...")
        
        conn = sqlite3.connect("trading_system.db")
        cursor = conn.cursor()
        
        query = """
        SELECT 
            id as '交易ID',
            signal_timestamp as '信号时间戳',
            direction as '交易方向',
            entry_price as '入场价格',
            exit_price as '出场价格',
            status as '结算状态',
            pnl as '盈亏金额',
            exit_timestamp as '结算时间戳',
            (julianday(exit_timestamp) - julianday(signal_timestamp)) * 24 * 60 as '结算时间差_分钟',
            suggested_bet as '投注金额',
            confidence_score as '置信度分数',
            symbol as '交易品种'
        FROM trade_history 
        WHERE status IN ('WIN', 'LOSS', 'TIMEOUT')
        AND exit_timestamp IS NOT NULL
        ORDER BY signal_timestamp ASC
        """
        
        cursor.execute(query)
        trades = cursor.fetchall()
        
        # 获取列名
        column_names = [description[0] for description in cursor.description]
        
        # 写入CSV文件
        import csv
        with open('settlement_results.csv', 'w', newline='', encoding='utf-8') as csvfile:
            writer = csv.writer(csvfile)
            writer.writerow(column_names)
            writer.writerows(trades)
        
        conn.close()
        
        logger.info(f"✅ 结算结果已导出到 settlement_results.csv 文件")
        logger.info(f"包含 {len(trades)} 条记录")
        
    except Exception as e:
        logger.error(f"导出CSV失败: {e}")

if __name__ == "__main__":
    # 查询并显示所有结算结果
    result = query_all_settlement_results()
    
    if result:
        # 导出到CSV文件
        export_to_csv()
        
        print(f"\n✅ 查询完成!")
        print(f"详细结果已显示在上方，同时已导出到 settlement_results.csv 文件")
    else:
        print("❌ 查询失败!")
