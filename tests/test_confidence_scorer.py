"""
Unit tests for confidence scoring system
"""

import pytest
import pandas as pd
import numpy as np
from datetime import datetime
import sys
import os

# Add project root to path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from quant.confidence_scorer import ConfidenceScorer, ConfidenceScore, SignalStrength


class TestConfidenceScorer:
    """Test cases for ConfidenceScorer class."""
    
    def setup_method(self):
        """Set up test fixtures."""
        self.scorer = ConfidenceScorer()
        
        # Create sample market data
        np.random.seed(42)
        dates = pd.date_range(start='2024-01-01', periods=100, freq='1h')
        
        self.sample_data = pd.DataFrame({
            'timestamp': dates,
            'open': np.random.uniform(40000, 45000, 100),
            'high': np.random.uniform(41000, 46000, 100),
            'low': np.random.uniform(39000, 44000, 100),
            'close': np.random.uniform(40000, 45000, 100),
            'volume': np.random.uniform(100, 1000, 100)
        }).set_index('timestamp')
        
        # Create trend data for testing
        trend_prices = np.linspace(40000, 45000, 100) + np.random.normal(0, 100, 100)
        self.trend_data = pd.DataFrame({
            'timestamp': dates,
            'open': trend_prices,
            'high': trend_prices + np.random.uniform(0, 200, 100),
            'low': trend_prices - np.random.uniform(0, 200, 100),
            'close': trend_prices,
            'volume': np.random.uniform(100, 1000, 100)
        }).set_index('timestamp')
    
    def test_init_default_config(self):
        """Test initialization with default configuration."""
        scorer = ConfidenceScorer()
        
        assert 'trend' in scorer.weights
        assert 'momentum' in scorer.weights
        assert 'volatility' in scorer.weights
        assert 'volume' in scorer.weights
        assert 'market_regime' in scorer.weights
        
        assert scorer.weights['trend'] == 0.30
        assert scorer.weights['momentum'] == 0.25
    
    def test_init_custom_config(self):
        """Test initialization with custom configuration."""
        custom_config = {
            'weights': {
                'trend': 0.5,
                'momentum': 0.3,
                'volatility': 0.1,
                'volume': 0.05,
                'market_regime': 0.05
            },
            'thresholds': {
                'minimum': 0.5,
                'strong': 0.75,
                'maximum': 0.9
            }
        }
        
        scorer = ConfidenceScorer(custom_config)
        
        assert scorer.weights['trend'] == 0.5
        assert scorer.weights['momentum'] == 0.3
        assert scorer.thresholds['minimum'] == 0.5
    
    def test_calculate_confidence_basic(self):
        """Test basic confidence calculation."""
        confidence_score = self.scorer.calculate_confidence(self.sample_data)
        
        assert isinstance(confidence_score, ConfidenceScore)
        assert 0.0 <= confidence_score.overall_confidence <= 1.0
        assert 0.0 <= confidence_score.trend_score <= 1.0
        assert 0.0 <= confidence_score.momentum_score <= 1.0
        assert 0.0 <= confidence_score.volatility_score <= 1.0
        assert 0.0 <= confidence_score.volume_score <= 1.0
        assert 0.0 <= confidence_score.market_regime_score <= 1.0
        assert isinstance(confidence_score.indicator_scores, dict)
        assert isinstance(confidence_score.timestamp, datetime)
    
    def test_calculate_confidence_with_trend(self):
        """Test confidence calculation with trending data."""
        confidence_score = self.scorer.calculate_confidence(self.trend_data)
        
        # Trend data should generally have higher trend scores
        assert confidence_score.trend_score >= 0.5
        assert isinstance(confidence_score, ConfidenceScore)
    
    def test_calculate_confidence_empty_data(self):
        """Test confidence calculation with empty data."""
        empty_data = pd.DataFrame({
            'open': [], 'high': [], 'low': [], 'close': [], 'volume': []
        })
        
        confidence_score = self.scorer.calculate_confidence(empty_data)
        
        # Should return low confidence for empty data due to default fallback values
        assert confidence_score.overall_confidence < 0.5
        assert confidence_score.trend_score == 0.5  # Default fallback
        assert confidence_score.momentum_score == 0.5  # Default fallback
    
    def test_individual_indicator_scores(self):
        """Test individual indicator scoring."""
        confidence_score = self.scorer.calculate_confidence(self.sample_data)
        
        # Check that individual scores are calculated
        indicator_scores = confidence_score.indicator_scores
        
        # Some indicators should be scored
        assert len(indicator_scores) > 0
        
        # All scores should be between 0 and 1
        for score in indicator_scores.values():
            assert 0.0 <= score <= 1.0
    
    def test_weighted_confidence_calculation(self):
        """Test weighted confidence calculation."""
        trend_score = 0.8
        momentum_score = 0.7
        volatility_score = 0.6
        volume_score = 0.5
        market_regime_score = 0.4
        
        weighted = self.scorer._calculate_weighted_confidence(
            trend_score, momentum_score, volatility_score,
            volume_score, market_regime_score
        )
        
        expected = (
            0.8 * 0.30 +  # trend
            0.7 * 0.25 +  # momentum
            0.6 * 0.20 +  # volatility
            0.5 * 0.15 +  # volume
            0.4 * 0.10    # market_regime
        )
        
        assert abs(weighted - expected) < 0.001
    
    def test_signal_strength_conversion(self):
        """Test signal strength conversion."""
        # Test strong signal
        strength = self.scorer.get_signal_strength(0.95)
        assert strength == SignalStrength.STRONG
        
        # Test medium signal
        strength = self.scorer.get_signal_strength(0.85)
        assert strength == SignalStrength.MEDIUM
        
        # Test weak signal
        strength = self.scorer.get_signal_strength(0.65)
        assert strength == SignalStrength.WEAK
        
        # Test below minimum
        strength = self.scorer.get_signal_strength(0.5)
        assert strength == SignalStrength.WEAK
    
    def test_market_regime_detection(self):
        """Test market regime detection."""
        regime = self.scorer._detect_market_regime(self.sample_data)
        
        # Should return a valid regime
        assert regime is not None
        assert hasattr(regime, 'value')
    
    def test_market_regime_adjustment(self):
        """Test market regime adjustment."""
        from quant.analysis.technical_indicators import MarketRegime
        
        # Test bullish adjustment
        adjusted = self.scorer._apply_market_regime_adjustment(0.7, MarketRegime.BULLISH)
        assert adjusted == 0.7 * 1.1  # 0.77
        
        # Test bearish adjustment
        adjusted = self.scorer._apply_market_regime_adjustment(0.7, MarketRegime.BEARISH)
        assert adjusted == 0.7 * 1.0  # 0.7
        
        # Test sideways adjustment
        adjusted = self.scorer._apply_market_regime_adjustment(0.7, MarketRegime.SIDEWAYS)
        assert adjusted == 0.7 * 0.9  # 0.63
        
        # Test bounds
        adjusted = self.scorer._apply_market_regime_adjustment(0.95, MarketRegime.BULLISH)
        assert adjusted == 1.0  # Should be capped at 1.0
        
        adjusted = self.scorer._apply_market_regime_adjustment(0.1, MarketRegime.VOLATILE)
        assert abs(adjusted - 0.08) < 0.001  # Allow for floating point precision
    
    def test_performance_tracking(self):
        """Test performance tracking."""
        # Calculate confidence to trigger performance tracking
        self.scorer.calculate_confidence(self.sample_data)
        
        stats = self.scorer.get_performance_stats()
        
        # Should have performance stats
        assert 'avg_calculation_time' in stats
        assert 'max_calculation_time' in stats
        assert 'min_calculation_time' in stats
        assert 'recent_scores_count' in stats
        assert 'avg_confidence' in stats
        
        # Should have tracked at least one calculation
        assert stats['recent_scores_count'] >= 1
    
    def test_config_update(self):
        """Test configuration update."""
        new_config = {
            'weights': {
                'trend': 0.4,
                'momentum': 0.3,
                'volatility': 0.2,
                'volume': 0.05,
                'market_regime': 0.05
            }
        }
        
        self.scorer.update_config(new_config)
        
        assert self.scorer.weights['trend'] == 0.4
        assert self.scorer.weights['momentum'] == 0.3
    
    def test_to_dict(self):
        """Test dictionary conversion."""
        result = self.scorer.to_dict()
        
        assert isinstance(result, dict)
        assert 'config' in result
        assert 'performance_stats' in result
    
    def test_string_representation(self):
        """Test string representation."""
        str_repr = str(self.scorer)
        
        assert isinstance(str_repr, str)
        assert 'ConfidenceScorer' in str_repr
        assert 'weights' in str_repr
        assert 'thresholds' in str_repr
    
    def test_confidence_score_dataclass(self):
        """Test ConfidenceScore dataclass."""
        score = ConfidenceScore(
            overall_confidence=0.8,
            trend_score=0.7,
            momentum_score=0.6,
            volatility_score=0.5,
            volume_score=0.4,
            market_regime_score=0.3,
            indicator_scores={'rsi': 0.7, 'macd': 0.8},
            calculation_details={'test': 'data'},
            timestamp=datetime.now()
        )
        
        # Test that all fields are accessible
        assert score.overall_confidence == 0.8
        assert score.trend_score == 0.7
        assert score.momentum_score == 0.6
        assert score.volatility_score == 0.5
        assert score.volume_score == 0.4
        assert score.market_regime_score == 0.3
        assert score.indicator_scores == {'rsi': 0.7, 'macd': 0.8}
        assert score.calculation_details == {'test': 'data'}
        assert isinstance(score.timestamp, datetime)


if __name__ == '__main__':
    pytest.main([__file__, '-v'])