#!/usr/bin/env python3
"""
分析数据库中已结算信号的准确性问题
检查结算数据的一致性和准确性
"""

import sqlite3
import os
from datetime import datetime, timedelta
import logging

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def analyze_settlement_accuracy():
    """分析已结算信号的准确性"""
    try:
        logger.info("🔍 开始分析数据库中已结算信号的准确性...")

        # 查找数据库文件
        db_path = None
        possible_paths = [
            "trading_system.db",
            "trading_data.db",
            "quant/trading_data.db",
            "data/trading_data.db"
        ]

        for path in possible_paths:
            if os.path.exists(path):
                db_path = path
                break

        if not db_path:
            logger.error("❌ 未找到数据库文件")
            return None

        logger.info(f"📊 使用数据库文件: {db_path}")

        # 连接数据库
        conn = sqlite3.connect(db_path)
        conn.row_factory = sqlite3.Row  # 使结果可以通过列名访问
        cursor = conn.cursor()

        # 1. 检查数据库表结构
        logger.info("\n📊 步骤1: 检查数据库表结构")
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
        tables = cursor.fetchall()
        logger.info(f"数据库中的表: {[table[0] for table in tables]}")

        # 2. 获取已结算的交易记录
        logger.info("\n📈 步骤2: 获取已结算的交易记录")
        cutoff_date = (datetime.utcnow() - timedelta(days=7)).isoformat()

        query = """
        SELECT * FROM trade_history
        WHERE status IN ('WIN', 'LOSS')
        AND signal_timestamp >= ?
        AND exit_timestamp IS NOT NULL
        ORDER BY signal_timestamp DESC
        """

        cursor.execute(query, (cutoff_date,))
        settled_trades = cursor.fetchall()

        logger.info(f"找到 {len(settled_trades)} 个已结算的交易记录")

        # 3. 分析结算数据的一致性问题
        logger.info("\n🔍 步骤3: 分析结算数据一致性")

        inconsistent_trades = []
        missing_data_trades = []
        timing_issues = []

        for trade in settled_trades:
            issues = []

            # 检查必要字段是否完整
            if trade['exit_price'] is None:
                issues.append("缺少退出价格")
            if trade['exit_timestamp'] is None:
                issues.append("缺少退出时间")
            if trade['pnl'] is None:
                issues.append("缺少盈亏数据")

            # 检查时间逻辑
            if trade['exit_timestamp'] and trade['signal_timestamp']:
                try:
                    signal_time = datetime.fromisoformat(trade['signal_timestamp'])
                    exit_time = datetime.fromisoformat(trade['exit_timestamp'])
                    time_diff = (exit_time - signal_time).total_seconds() / 60
                    expected_settlement_time = 10  # 预期10分钟后结算

                    if abs(time_diff - expected_settlement_time) > 2:  # 允许2分钟误差
                        issues.append(f"结算时间异常: {time_diff:.1f}分钟 (预期: {expected_settlement_time}分钟)")
                        timing_issues.append({
                            'trade_id': trade['id'],
                            'signal_time': trade['signal_timestamp'],
                            'exit_time': trade['exit_timestamp'],
                            'time_diff': time_diff
                        })
                except Exception as e:
                    issues.append(f"时间解析错误: {e}")

            # 检查价格逻辑 - 修正计算公式
            if trade['entry_price'] and trade['exit_price'] and trade['suggested_bet']:
                if trade['direction'] == "LONG":
                    # 做多：(出场价 - 入场价) / 入场价 * 投注金额
                    price_change_pct = (trade['exit_price'] - trade['entry_price']) / trade['entry_price']
                    expected_pnl = price_change_pct * trade['suggested_bet']
                else:  # SHORT
                    # 做空：(入场价 - 出场价) / 入场价 * 投注金额
                    price_change_pct = (trade['entry_price'] - trade['exit_price']) / trade['entry_price']
                    expected_pnl = price_change_pct * trade['suggested_bet']

                if trade['pnl'] is not None and abs(trade['pnl'] - expected_pnl) > 0.1:  # 增加容差到0.1
                    issues.append(f"盈亏计算异常: 实际{trade['pnl']:.2f}, 预期{expected_pnl:.2f}")

            if issues:
                if any("缺少" in issue for issue in issues):
                    missing_data_trades.append({
                        'trade_id': trade['id'],
                        'issues': issues,
                        'trade': dict(trade)
                    })
                else:
                    inconsistent_trades.append({
                        'trade_id': trade['id'],
                        'issues': issues,
                        'trade': dict(trade)
                    })

        # 4. 报告分析结果
        logger.info("\n📋 步骤4: 分析结果报告")
        logger.info(f"总计已结算交易: {len(settled_trades)}")
        logger.info(f"数据缺失的交易: {len(missing_data_trades)}")
        logger.info(f"数据不一致的交易: {len(inconsistent_trades)}")
        logger.info(f"时间异常的交易: {len(timing_issues)}")

        # 详细报告问题交易
        if missing_data_trades:
            logger.warning("\n❌ 数据缺失的交易:")
            for item in missing_data_trades[:5]:  # 只显示前5个
                trade = item['trade']
                logger.warning(f"  交易ID {trade['id']}: {', '.join(item['issues'])}")
                logger.warning(f"    信号时间: {trade['signal_timestamp']}")
                logger.warning(f"    状态: {trade['status']}")

        if inconsistent_trades:
            logger.warning("\n⚠️ 数据不一致的交易:")
            for item in inconsistent_trades[:5]:  # 只显示前5个
                trade = item['trade']
                logger.warning(f"  交易ID {trade['id']}: {', '.join(item['issues'])}")
                logger.warning(f"    方向: {trade['direction']}, 入场: {trade['entry_price']}, 出场: {trade['exit_price']}")
                logger.warning(f"    盈亏: {trade['pnl']}, 状态: {trade['status']}")

        if timing_issues:
            logger.warning("\n⏰ 时间异常的交易:")
            for item in timing_issues[:5]:  # 只显示前5个
                logger.warning(f"  交易ID {item['trade_id']}: 结算时间差异 {item['time_diff']:.1f}分钟")
                logger.warning(f"    信号时间: {item['signal_time']}")
                logger.warning(f"    结算时间: {item['exit_time']}")

        # 5. 检查待结算交易
        logger.info("\n🔧 步骤5: 检查待结算交易")
        pending_query = """
        SELECT * FROM trade_history
        WHERE status = 'PENDING'
        AND signal_timestamp < ?
        ORDER BY signal_timestamp DESC
        """

        cutoff_for_pending = (datetime.utcnow() - timedelta(minutes=15)).isoformat()
        cursor.execute(pending_query, (cutoff_for_pending,))
        overdue_trades = cursor.fetchall()

        logger.info(f"发现 {len(overdue_trades)} 个超时未结算的交易")

        if overdue_trades:
            logger.warning("\n⏰ 超时未结算的交易:")
            for trade in overdue_trades[:5]:
                logger.warning(f"  交易ID {trade['id']}: 信号时间 {trade['signal_timestamp']}")

        conn.close()

        return {
            'total_settled': len(settled_trades),
            'missing_data': len(missing_data_trades),
            'inconsistent_data': len(inconsistent_trades),
            'timing_issues': len(timing_issues),
            'overdue_pending': len(overdue_trades),
            'missing_data_trades': missing_data_trades,
            'inconsistent_trades': inconsistent_trades,
            'timing_issues': timing_issues,
            'overdue_trades': [dict(trade) for trade in overdue_trades]
        }

    except Exception as e:
        logger.error(f"分析过程中发生错误: {e}")
        import traceback
        logger.error(f"详细错误信息: {traceback.format_exc()}")
        return None

if __name__ == "__main__":
    result = analyze_settlement_accuracy()
    if result:
        logger.info("\n✅ 分析完成!")
        total_issues = result['missing_data'] + result['inconsistent_data'] + len(result['timing_issues']) + result['overdue_pending']
        logger.info(f"发现的问题总数: {total_issues}")

        if total_issues > 0:
            logger.warning("\n🔧 建议的修复措施:")
            logger.warning("1. 对于数据缺失的交易，需要重新计算结算结果")
            logger.warning("2. 对于数据不一致的交易，需要验证计算逻辑")
            logger.warning("3. 对于时间异常的交易，需要检查结算时机")
            logger.warning("4. 对于超时未结算的交易，需要强制结算")
    else:
        logger.error("❌ 分析失败!")
