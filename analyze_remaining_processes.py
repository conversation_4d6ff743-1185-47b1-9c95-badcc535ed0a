#!/usr/bin/env python3
"""
分析剩余的主要交易进程
确保系统正常运行且无重复信号
"""

import subprocess
import psutil
import os
import time
from datetime import datetime
import logging

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def analyze_main_trading_processes():
    """分析主要的交易进程"""
    try:
        logger.info("🔍 分析剩余的主要交易进程...")
        
        # 查找Python进程
        python_processes = []
        for proc in psutil.process_iter(['pid', 'name', 'cmdline', 'create_time', 'cwd']):
            try:
                if proc.info['name'] == 'Python' and proc.info['cmdline']:
                    cmdline = ' '.join(proc.info['cmdline'])
                    if 'main.py' in cmdline or 'quant' in cmdline.lower():
                        python_processes.append({
                            'pid': proc.info['pid'],
                            'cmdline': cmdline,
                            'create_time': datetime.fromtimestamp(proc.info['create_time']),
                            'cwd': proc.info['cwd']
                        })
            except (psutil.NoSuchProcess, psutil.AccessDenied):
                continue
        
        print("\n" + "="*100)
        print("🔍 主要交易进程分析")
        print("="*100)
        
        if not python_processes:
            print("✅ 未发现主要交易进程运行")
            return []
        
        print(f"发现 {len(python_processes)} 个主要交易进程:")
        
        for i, proc in enumerate(python_processes, 1):
            print(f"\n进程 {i}:")
            print(f"  PID: {proc['pid']}")
            print(f"  启动时间: {proc['create_time']}")
            print(f"  工作目录: {proc['cwd']}")
            print(f"  命令行: {proc['cmdline']}")
            
            # 检查进程状态
            try:
                p = psutil.Process(proc['pid'])
                print(f"  状态: {p.status()}")
                print(f"  CPU使用率: {p.cpu_percent():.1f}%")
                print(f"  内存使用: {p.memory_info().rss / 1024 / 1024:.1f} MB")
                
                # 检查网络连接
                connections = p.net_connections()
                if connections:
                    print(f"  网络连接: {len(connections)} 个")
                    for conn in connections[:3]:  # 显示前3个
                        if conn.laddr:
                            print(f"    - {conn.laddr} -> {conn.raddr if conn.raddr else 'N/A'} ({conn.status})")
                
            except (psutil.NoSuchProcess, psutil.AccessDenied):
                print("  状态: 无法访问")
        
        return python_processes
        
    except Exception as e:
        logger.error(f"分析主要交易进程失败: {e}")
        return []

def check_signal_generation_activity():
    """检查信号生成活动"""
    try:
        logger.info("📊 检查最近的信号生成活动...")
        
        # 检查数据库中最近的信号
        import sqlite3
        
        conn = sqlite3.connect("trading_system.db")
        cursor = conn.cursor()
        
        # 获取最近30分钟的信号
        cursor.execute("""
            SELECT signal_timestamp, COUNT(*) as count
            FROM trade_history 
            WHERE signal_timestamp > datetime('now', '-30 minutes')
            GROUP BY signal_timestamp
            ORDER BY signal_timestamp DESC
        """)
        
        recent_signals = cursor.fetchall()
        
        print("\n" + "="*80)
        print("📊 最近30分钟的信号生成活动")
        print("="*80)
        
        if not recent_signals:
            print("✅ 最近30分钟无新信号生成")
        else:
            print("⚠️ 最近30分钟的信号:")
            for signal in recent_signals:
                signal_time = datetime.fromisoformat(signal[0])
                count = signal[1]
                print(f"  {signal_time.strftime('%Y-%m-%d %H:%M:%S')}: {count} 个信号")
                
                if count > 1:
                    print(f"    ⚠️ 发现重复信号! 同一时间生成了 {count} 个信号")
        
        # 检查是否有重复的信号时间戳
        cursor.execute("""
            SELECT signal_timestamp, COUNT(*) as count
            FROM trade_history 
            GROUP BY signal_timestamp
            HAVING COUNT(*) > 1
            ORDER BY signal_timestamp DESC
            LIMIT 10
        """)
        
        duplicate_signals = cursor.fetchall()
        
        if duplicate_signals:
            print(f"\n⚠️ 发现 {len(duplicate_signals)} 个重复信号时间戳:")
            for signal in duplicate_signals:
                signal_time = datetime.fromisoformat(signal[0])
                count = signal[1]
                print(f"  {signal_time.strftime('%Y-%m-%d %H:%M:%S')}: {count} 个重复信号")
        else:
            print("\n✅ 未发现重复的信号时间戳")
        
        conn.close()
        
        return len(duplicate_signals) if duplicate_signals else 0
        
    except Exception as e:
        logger.error(f"检查信号生成活动失败: {e}")
        return -1

def check_system_health():
    """检查系统健康状态"""
    try:
        logger.info("🏥 检查系统健康状态...")
        
        print("\n" + "="*80)
        print("🏥 系统健康状态检查")
        print("="*80)
        
        # 检查数据库文件
        db_path = "trading_system.db"
        if os.path.exists(db_path):
            db_size = os.path.getsize(db_path) / 1024 / 1024
            print(f"✅ 数据库文件: {db_path} ({db_size:.2f} MB)")
        else:
            print(f"❌ 数据库文件不存在: {db_path}")
        
        # 检查日志文件
        log_files = []
        for root, dirs, files in os.walk('.'):
            for file in files:
                if file.endswith('.log'):
                    log_files.append(os.path.join(root, file))
        
        if log_files:
            print(f"📄 日志文件: {len(log_files)} 个")
            for log_file in log_files[:5]:  # 显示前5个
                size = os.path.getsize(log_file) / 1024
                print(f"  - {log_file} ({size:.1f} KB)")
        else:
            print("📄 未发现日志文件")
        
        # 检查配置文件
        config_files = ['config.json', 'settings.py', 'config.py']
        for config_file in config_files:
            if os.path.exists(config_file):
                print(f"⚙️ 配置文件: {config_file}")
        
        # 检查端口占用
        print(f"\n🌐 网络端口检查:")
        common_ports = [8000, 8080, 3000, 5000, 9000]
        for port in common_ports:
            try:
                result = subprocess.run(['lsof', '-i', f':{port}'], 
                                      capture_output=True, text=True, timeout=5)
                if result.stdout.strip():
                    print(f"  端口 {port}: 被占用")
                else:
                    print(f"  端口 {port}: 空闲")
            except:
                print(f"  端口 {port}: 检查失败")
        
        return True
        
    except Exception as e:
        logger.error(f"系统健康检查失败: {e}")
        return False

def create_process_monitor():
    """创建进程监控脚本"""
    monitor_script = '''#!/usr/bin/env python3
"""
进程监控脚本 - 持续监控重复进程
"""

import psutil
import time
from datetime import datetime
import logging

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def monitor_trading_processes():
    """监控交易进程"""
    while True:
        try:
            # 查找main.py进程
            main_processes = []
            for proc in psutil.process_iter(['pid', 'name', 'cmdline', 'create_time']):
                try:
                    if proc.info['name'] == 'Python' and proc.info['cmdline']:
                        cmdline = ' '.join(proc.info['cmdline'])
                        if 'main.py' in cmdline:
                            main_processes.append({
                                'pid': proc.info['pid'],
                                'create_time': datetime.fromtimestamp(proc.info['create_time'])
                            })
                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    continue
            
            timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            
            if len(main_processes) > 1:
                logger.warning(f"[{timestamp}] ⚠️ 发现 {len(main_processes)} 个main.py进程!")
                for proc in main_processes:
                    logger.warning(f"  PID {proc['pid']}, 启动时间: {proc['create_time']}")
            elif len(main_processes) == 1:
                logger.info(f"[{timestamp}] ✅ 正常运行 1 个main.py进程 (PID {main_processes[0]['pid']})")
            else:
                logger.info(f"[{timestamp}] ℹ️ 未发现main.py进程运行")
            
            time.sleep(60)  # 每分钟检查一次
            
        except KeyboardInterrupt:
            logger.info("监控已停止")
            break
        except Exception as e:
            logger.error(f"监控过程中发生错误: {e}")
            time.sleep(60)

if __name__ == "__main__":
    monitor_trading_processes()
'''
    
    with open('monitor_processes.py', 'w', encoding='utf-8') as f:
        f.write(monitor_script)
    
    logger.info("✅ 进程监控脚本已创建: monitor_processes.py")

def main():
    """主函数"""
    print("🔍 剩余进程分析和系统健康检查")
    print("="*50)
    
    # 1. 分析主要交易进程
    processes = analyze_main_trading_processes()
    
    # 2. 检查信号生成活动
    duplicate_signals = check_signal_generation_activity()
    
    # 3. 系统健康检查
    health_ok = check_system_health()
    
    # 4. 创建监控脚本
    create_process_monitor()
    
    # 5. 总结
    print("\n" + "="*80)
    print("📋 分析总结")
    print("="*80)
    
    print(f"主要交易进程数: {len(processes)}")
    
    if duplicate_signals > 0:
        print(f"⚠️ 发现重复信号: {duplicate_signals} 个")
    elif duplicate_signals == 0:
        print("✅ 未发现重复信号")
    else:
        print("❌ 信号检查失败")
    
    print(f"系统健康状态: {'✅ 正常' if health_ok else '❌ 异常'}")
    
    # 建议
    print(f"\n💡 建议:")
    if len(processes) > 1:
        print("1. 检查是否有多个main.py进程在运行")
        print("2. 确认只保留一个主进程")
    
    if duplicate_signals > 0:
        print("3. 检查信号生成逻辑，避免重复信号")
        print("4. 实现信号去重机制")
    
    print("5. 使用 monitor_processes.py 持续监控进程状态")
    print("6. 定期检查数据库中的重复信号")

if __name__ == "__main__":
    main()
