#!/usr/bin/env python3
"""
修复结算时机问题
主要解决结算时间不准确和超时未结算的问题
"""

import sqlite3
import os
from datetime import datetime, timedelta
import logging

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def fix_settlement_timing_issues():
    """修复结算时机问题"""
    try:
        logger.info("🔧 开始修复结算时机问题...")
        
        # 连接数据库
        db_path = "trading_system.db"
        if not os.path.exists(db_path):
            logger.error("❌ 未找到数据库文件")
            return False
        
        conn = sqlite3.connect(db_path)
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()
        
        # 1. 分析结算时机问题
        logger.info("\n📊 步骤1: 分析结算时机问题")
        
        cursor.execute("""
            SELECT id, signal_timestamp, exit_timestamp, status,
                   (julianday(exit_timestamp) - julianday(signal_timestamp)) * 24 * 60 as time_diff_minutes
            FROM trade_history 
            WHERE status IN ('WIN', 'LOSS') 
            AND exit_timestamp IS NOT NULL
            ORDER BY signal_timestamp DESC
        """)
        
        settled_trades = cursor.fetchall()
        timing_issues = []
        
        for trade in settled_trades:
            time_diff = trade['time_diff_minutes']
            if abs(time_diff - 10) > 2:  # 超出10分钟±2分钟的范围
                timing_issues.append({
                    'id': trade['id'],
                    'time_diff': time_diff,
                    'signal_time': trade['signal_timestamp'],
                    'exit_time': trade['exit_timestamp']
                })
        
        logger.info(f"发现 {len(timing_issues)} 个结算时机异常的交易")
        
        # 2. 处理超时未结算的交易
        logger.info("\n🔧 步骤2: 处理超时未结算的交易")
        
        cutoff_time = (datetime.utcnow() - timedelta(minutes=15)).isoformat()
        cursor.execute("""
            SELECT * FROM trade_history 
            WHERE status = 'PENDING' 
            AND signal_timestamp < ?
            ORDER BY signal_timestamp
        """, (cutoff_time,))
        
        overdue_trades = cursor.fetchall()
        logger.info(f"发现 {len(overdue_trades)} 个超时未结算的交易")
        
        # 将超时交易标记为需要手动处理
        for trade in overdue_trades:
            signal_time = datetime.fromisoformat(trade['signal_timestamp'])
            time_since_signal = (datetime.utcnow() - signal_time).total_seconds() / 60
            
            logger.warning(f"  交易ID {trade['id']}: 超时 {time_since_signal:.1f} 分钟")
            logger.warning(f"    信号时间: {trade['signal_timestamp']}")
            logger.warning(f"    方向: {trade['direction']}, 入场价: {trade['entry_price']}")
        
        # 3. 创建结算时机监控报告
        logger.info("\n📋 步骤3: 生成结算时机监控报告")
        
        # 统计结算时机分布
        cursor.execute("""
            SELECT 
                CASE 
                    WHEN (julianday(exit_timestamp) - julianday(signal_timestamp)) * 24 * 60 < 8 THEN '< 8分钟'
                    WHEN (julianday(exit_timestamp) - julianday(signal_timestamp)) * 24 * 60 BETWEEN 8 AND 12 THEN '8-12分钟'
                    WHEN (julianday(exit_timestamp) - julianday(signal_timestamp)) * 24 * 60 BETWEEN 12 AND 15 THEN '12-15分钟'
                    ELSE '> 15分钟'
                END as time_range,
                COUNT(*) as count
            FROM trade_history 
            WHERE status IN ('WIN', 'LOSS') 
            AND exit_timestamp IS NOT NULL
            GROUP BY time_range
        """)
        
        timing_distribution = cursor.fetchall()
        
        logger.info("结算时机分布:")
        for row in timing_distribution:
            logger.info(f"  {row['time_range']}: {row['count']} 个交易")
        
        # 4. 创建改进建议
        logger.info("\n💡 步骤4: 改进建议")
        
        suggestions = []
        
        if len(timing_issues) > 0:
            suggestions.append("优化结算时机控制，确保在10分钟±1分钟内结算")
        
        if len(overdue_trades) > 0:
            suggestions.append("实现超时交易的自动强制结算机制")
            suggestions.append("添加结算失败的重试机制")
        
        suggestions.append("增加结算时机的实时监控和告警")
        suggestions.append("优化结算窗口的时间控制逻辑")
        
        for i, suggestion in enumerate(suggestions, 1):
            logger.info(f"  {i}. {suggestion}")
        
        conn.close()
        
        return {
            'timing_issues': len(timing_issues),
            'overdue_trades': len(overdue_trades),
            'total_settled': len(settled_trades),
            'suggestions': suggestions
        }
        
    except Exception as e:
        logger.error(f"修复过程中发生错误: {e}")
        import traceback
        logger.error(f"详细错误信息: {traceback.format_exc()}")
        return None

def create_settlement_timing_monitor():
    """创建结算时机监控组件"""
    logger.info("\n🔧 创建结算时机监控组件...")
    
    monitor_content = '''"""
结算时机监控组件
监控和优化结算时机的准确性
"""

import sqlite3
from datetime import datetime, timedelta
from typing import Dict, List, Any
import logging

logger = logging.getLogger(__name__)

class SettlementTimingMonitor:
    """结算时机监控类"""
    
    def __init__(self, db_path: str = "trading_system.db"):
        self.db_path = db_path
        self.target_settlement_time = 10  # 目标结算时间（分钟）
        self.tolerance = 1  # 允许的时间误差（分钟）
    
    def check_settlement_timing(self) -> Dict[str, Any]:
        """检查结算时机准确性"""
        try:
            conn = sqlite3.connect(self.db_path)
            conn.row_factory = sqlite3.Row
            cursor = conn.cursor()
            
            # 检查最近的结算时机
            cursor.execute("""
                SELECT id, signal_timestamp, exit_timestamp,
                       (julianday(exit_timestamp) - julianday(signal_timestamp)) * 24 * 60 as time_diff
                FROM trade_history 
                WHERE status IN ('WIN', 'LOSS') 
                AND exit_timestamp IS NOT NULL
                AND signal_timestamp > datetime('now', '-24 hours')
                ORDER BY signal_timestamp DESC
            """)
            
            recent_settlements = cursor.fetchall()
            
            timing_issues = []
            for trade in recent_settlements:
                time_diff = trade['time_diff']
                if abs(time_diff - self.target_settlement_time) > self.tolerance:
                    timing_issues.append({
                        'trade_id': trade['id'],
                        'expected_time': self.target_settlement_time,
                        'actual_time': time_diff,
                        'deviation': time_diff - self.target_settlement_time
                    })
            
            conn.close()
            
            return {
                'total_settlements': len(recent_settlements),
                'timing_issues': len(timing_issues),
                'accuracy_rate': (len(recent_settlements) - len(timing_issues)) / len(recent_settlements) * 100 if recent_settlements else 0,
                'issues': timing_issues
            }
            
        except Exception as e:
            logger.error(f"检查结算时机失败: {e}")
            return {'error': str(e)}
    
    def get_overdue_trades(self) -> List[Dict[str, Any]]:
        """获取超时未结算的交易"""
        try:
            conn = sqlite3.connect(self.db_path)
            conn.row_factory = sqlite3.Row
            cursor = conn.cursor()
            
            cutoff_time = (datetime.utcnow() - timedelta(minutes=15)).isoformat()
            cursor.execute("""
                SELECT * FROM trade_history 
                WHERE status = 'PENDING' 
                AND signal_timestamp < ?
                ORDER BY signal_timestamp
            """, (cutoff_time,))
            
            overdue_trades = [dict(trade) for trade in cursor.fetchall()]
            conn.close()
            
            return overdue_trades
            
        except Exception as e:
            logger.error(f"获取超时交易失败: {e}")
            return []
    
    def generate_timing_report(self) -> str:
        """生成结算时机报告"""
        timing_check = self.check_settlement_timing()
        overdue_trades = self.get_overdue_trades()
        
        report = f"""
结算时机监控报告
================
生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

最近24小时结算统计:
- 总结算数: {timing_check.get('total_settlements', 0)}
- 时机异常数: {timing_check.get('timing_issues', 0)}
- 时机准确率: {timing_check.get('accuracy_rate', 0):.1f}%

当前超时未结算交易: {len(overdue_trades)}

建议措施:
1. 优化结算时机控制逻辑
2. 实现超时交易自动处理
3. 增加结算监控告警
"""
        return report
'''
    
    # 保存监控组件
    os.makedirs('quant/monitoring', exist_ok=True)
    with open('quant/monitoring/settlement_timing_monitor.py', 'w', encoding='utf-8') as f:
        f.write(monitor_content)
    
    logger.info("✅ 结算时机监控组件已创建: quant/monitoring/settlement_timing_monitor.py")

if __name__ == "__main__":
    # 执行修复
    result = fix_settlement_timing_issues()
    
    if result:
        # 创建监控组件
        create_settlement_timing_monitor()
        
        logger.info("\n🎉 结算时机修复完成!")
        logger.info(f"发现的问题:")
        logger.info(f"  - 时机异常交易: {result['timing_issues']}")
        logger.info(f"  - 超时未结算交易: {result['overdue_trades']}")
        logger.info(f"  - 总已结算交易: {result['total_settled']}")
    else:
        logger.error("❌ 修复失败!")
