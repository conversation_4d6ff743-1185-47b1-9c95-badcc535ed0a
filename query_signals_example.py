#!/usr/bin/env python3
"""
交易信号数据查询示例脚本

演示如何使用现有的数据库管理器查询特定日期的交易信号数据
"""

from datetime import datetime, date
from typing import Any, List
import json

from quant.database_manager import db


def query_signals_by_date(target_date: date) -> List[dict[str, Any]]:
    """
    查询特定日期的所有交易信号
    
    Args:
        target_date: 目标日期
        
    Returns:
        交易信号列表
    """
    try:
        # 构建时间范围
        start_datetime = datetime.combine(target_date, datetime.min.time())
        end_datetime = datetime.combine(target_date, datetime.max.time())
        
        # 使用优化的查询方法
        filters = {
            "start_date": start_datetime,
            "end_date": end_datetime
        }
        
        signals = db.get_optimized_trade_history(
            limit=1000,  # 足够大的限制以获取所有数据
            offset=0,
            filters=filters,
            sort_by="signal_timestamp",
            sort_desc=False  # 按时间升序排列
        )
        
        return signals
        
    except Exception as e:
        print(f"查询信号数据时发生错误: {e}")
        return []


def get_signal_statistics(signals: List[dict[str, Any]]) -> dict[str, Any]:
    """
    计算信号统计信息
    
    Args:
        signals: 信号列表
        
    Returns:
        统计信息字典
    """
    if not signals:
        return {"message": "没有找到信号数据"}
    
    total_signals = len(signals)
    long_signals = len([s for s in signals if s["direction"] == "LONG"])
    short_signals = len([s for s in signals if s["direction"] == "SHORT"])
    
    confidence_scores = [s["confidence_score"] for s in signals if s["confidence_score"]]
    avg_confidence = sum(confidence_scores) / len(confidence_scores) if confidence_scores else 0
    min_confidence = min(confidence_scores) if confidence_scores else 0
    max_confidence = max(confidence_scores) if confidence_scores else 0
    
    # 状态统计
    status_counts = {}
    for signal in signals:
        status = signal.get("status", "UNKNOWN")
        status_counts[status] = status_counts.get(status, 0) + 1
    
    return {
        "total_signals": total_signals,
        "long_signals": long_signals,
        "short_signals": short_signals,
        "long_ratio": f"{long_signals/total_signals*100:.1f}%" if total_signals > 0 else "0%",
        "avg_confidence": round(avg_confidence, 4),
        "min_confidence": round(min_confidence, 4),
        "max_confidence": round(max_confidence, 4),
        "status_distribution": status_counts,
        "date_range": {
            "start": signals[0]["signal_timestamp"] if signals else None,
            "end": signals[-1]["signal_timestamp"] if signals else None
        }
    }


def get_detailed_signal_info(signal_id: int) -> dict[str, Any]:
    """
    获取单个信号的详细信息
    
    Args:
        signal_id: 信号ID
        
    Returns:
        详细信号信息
    """
    try:
        with db.get_session() as session:
            from quant.database_manager import TradeHistory
            
            trade = session.query(TradeHistory).filter(TradeHistory.id == signal_id).first()
            
            if not trade:
                return {"error": f"未找到ID为 {signal_id} 的信号"}
            
            # 解析JSON字段
            try:
                confirmed_indicators = json.loads(trade.confirmed_indicators) if trade.confirmed_indicators else []
                decision_details = json.loads(trade.decision_details) if trade.decision_details else {}
                confidence_breakdown = json.loads(trade.confidence_breakdown) if trade.confidence_breakdown else {}
            except json.JSONDecodeError:
                confirmed_indicators = []
                decision_details = {}
                confidence_breakdown = {}
            
            return {
                "basic_info": {
                    "id": trade.id,
                    "signal_timestamp": trade.signal_timestamp.isoformat(),
                    "symbol": trade.symbol,
                    "direction": trade.direction,
                    "entry_price": trade.entry_price,
                    "confidence_score": trade.confidence_score,
                    "market_state": trade.market_state,
                    "trigger_pattern": trade.trigger_pattern,
                    "suggested_bet": trade.suggested_bet,
                    "status": trade.status,
                    "exit_price": trade.exit_price,
                    "exit_timestamp": trade.exit_timestamp.isoformat() if trade.exit_timestamp else None,
                    "pnl": trade.pnl
                },
                "confidence_analysis": {
                    "overall_confidence": trade.confidence_score,
                    "market_regime_score": trade.market_regime_score,
                    "trend_strength_score": trade.trend_strength_score,
                    "momentum_score": trade.momentum_score,
                    "volatility_score": trade.volatility_score,
                    "volume_score": trade.volume_score,
                    "signal_strength": trade.signal_strength,
                    "detailed_breakdown": confidence_breakdown
                },
                "trading_details": {
                    "confirmed_indicators": confirmed_indicators,
                    "decision_details": decision_details,
                    "calculation_time_ms": trade.calculation_time_ms
                }
            }
            
    except Exception as e:
        return {"error": f"获取信号详情时发生错误: {e}"}


def main():
    """主函数 - 演示查询功能"""
    print("=" * 60)
    print("交易信号数据查询示例")
    print("=" * 60)
    
    # 查询2025年8月7日的信号
    target_date = date(2025, 8, 7)
    print(f"\n📅 查询日期: {target_date.isoformat()}")
    
    # 查询信号
    signals = query_signals_by_date(target_date)
    
    if not signals:
        print("❌ 没有找到指定日期的信号数据")
        return
    
    # 显示统计信息
    stats = get_signal_statistics(signals)
    print(f"\n📊 信号统计:")
    print(f"   总信号数: {stats['total_signals']}")
    print(f"   做多信号: {stats['long_signals']} ({stats['long_ratio']})")
    print(f"   做空信号: {stats['short_signals']}")
    print(f"   平均置信度: {stats['avg_confidence']}")
    print(f"   置信度范围: {stats['min_confidence']} - {stats['max_confidence']}")
    print(f"   状态分布: {stats['status_distribution']}")
    
    # 显示前10个信号
    print(f"\n📈 前10个信号:")
    print("-" * 100)
    print(f"{'ID':>4} {'时间':>20} {'方向':>6} {'价格':>10} {'置信度':>8} {'状态':>8} {'建议金额':>8}")
    print("-" * 100)
    
    for signal in signals[:10]:
        timestamp = signal["signal_timestamp"][:19]  # 只显示到秒
        direction = signal["direction"]
        price = f"{signal['entry_price']:,.2f}"
        confidence = f"{signal['confidence_score']:.4f}"
        status = signal.get("status", "PENDING")
        bet = f"{signal.get('suggested_bet', 0):.1f}"
        
        print(f"{signal['id']:>4} {timestamp:>20} {direction:>6} {price:>10} {confidence:>8} {status:>8} {bet:>8}")
    
    # 显示一个信号的详细信息
    if signals:
        first_signal_id = signals[0]["id"]
        print(f"\n🔍 信号ID {first_signal_id} 的详细信息:")
        detailed_info = get_detailed_signal_info(first_signal_id)
        
        if "error" not in detailed_info:
            basic = detailed_info["basic_info"]
            confidence = detailed_info["confidence_analysis"]
            
            print(f"   基本信息:")
            print(f"     交易对: {basic['symbol']}")
            print(f"     方向: {basic['direction']}")
            print(f"     入场价: ${basic['entry_price']:,.2f}")
            print(f"     信号时间: {basic['signal_timestamp']}")
            print(f"     触发模式: {basic['trigger_pattern']}")
            
            print(f"   置信度分析:")
            print(f"     整体置信度: {confidence['overall_confidence']:.4f}")
            print(f"     市场制度评分: {confidence['market_regime_score']:.4f}")
            print(f"     趋势强度评分: {confidence['trend_strength_score']:.4f}")
            print(f"     动量评分: {confidence['momentum_score']:.4f}")
            print(f"     波动率评分: {confidence['volatility_score']:.4f}")
            print(f"     成交量评分: {confidence['volume_score']:.4f}")
            print(f"     信号强度: {confidence['signal_strength']}")
            
            trading = detailed_info["trading_details"]
            print(f"   交易详情:")
            print(f"     确认指标: {trading['confirmed_indicators']}")
            print(f"     建议金额: ${basic['suggested_bet']}")
            print(f"     当前状态: {basic['status']}")
        else:
            print(f"   ❌ {detailed_info['error']}")


if __name__ == "__main__":
    main()