# -*- coding:utf-8 -*-

try:
	from setuptools import setup
except ImportError:
	from distutils.core import setup

setup(
	name="hertelquant",
	version="1.0.4",
	packages=[
		"quant",
		"quant/platform",
		"quant/utils",
	],
	platforms="any",
	description="Event-driven quantitative trading framework based on multithreading.",
	url="https://github.com/<PERSON>-<PERSON>/HertelQuant",
	author="<PERSON><PERSON><PERSON><PERSON>",
	author_email="<EMAIL>",
	keywords=[
		"hertelquant",
		"event-driven",
		"multithreading",
		"trade",
		"digital currency",
	],
	install_requires=[
		"requests==2.25.1",
		"pymongo==3.11.3",
		"websocket-client==0.57.0",
	],
)
